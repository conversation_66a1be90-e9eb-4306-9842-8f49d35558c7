"""
GAN模型评估器
评估生成谱面的质量、难度匹配度、音乐性等
"""

import torch
import numpy as np
import librosa
from pathlib import Path
from typing import Dict, List, Any, Optional
import logging
from scipy.stats import pearsonr
import matplotlib.pyplot as plt
import seaborn as sns

from ..models.audio_chart_gan import AudioChartGAN
from ..chart_generation.chart_data import ChartData, ChartMetadata, NoteInfo

logger = logging.getLogger(__name__)


class GANEvaluator:
    """GAN模型评估器"""
    
    def __init__(self, model_path: str, device: str = "cuda"):
        """
        初始化评估器
        
        Args:
            model_path: 模型文件路径
            device: 计算设备
        """
        self.device = device
        self.model = self._load_model(model_path)
        self.model.eval()
        
    def _load_model(self, model_path: str) -> AudioChartGAN:
        """加载模型"""
        checkpoint = torch.load(model_path, map_location=self.device)
        
        # 从配置重建模型
        config = checkpoint.get('config', {})
        model_config = config.get('model', {})
        
        model = AudioChartGAN(
            track_count=model_config.get('track_count', 4),
            max_length=model_config.get('max_length', 2000),
            use_wgan=model_config.get('use_wgan', True)
        ).to(self.device)
        
        model.load_state_dict(checkpoint['model_state_dict'])
        logger.info(f"模型已加载: {model_path}")
        
        return model
    
    def evaluate_single_audio(self, audio_path: str, difficulty: int = 5) -> Dict[str, Any]:
        """
        评估单个音频文件的生成结果
        
        Args:
            audio_path: 音频文件路径
            difficulty: 目标难度
            
        Returns:
            Dict: 评估结果
        """
        logger.info(f"评估音频: {audio_path}")
        
        # 生成谱面
        generated_chart = self.model.generate_chart(audio_path, difficulty)
        
        # 加载音频进行分析
        y, sr = librosa.load(audio_path, sr=22050)
        
        # 计算各种评估指标
        results = {
            'audio_path': audio_path,
            'target_difficulty': difficulty,
            'generated_chart_shape': generated_chart.shape,
            'metrics': {}
        }
        
        # 1. 音符密度分析
        results['metrics']['note_density'] = self._calculate_note_density(generated_chart)
        
        # 2. 难度评估
        results['metrics']['estimated_difficulty'] = self._estimate_difficulty(generated_chart)
        
        # 3. 音乐匹配度
        results['metrics']['music_alignment'] = self._calculate_music_alignment(
            generated_chart, y, sr
        )
        
        # 4. 节拍一致性
        results['metrics']['rhythm_consistency'] = self._calculate_rhythm_consistency(
            generated_chart, y, sr
        )
        
        # 5. 可玩性评分
        results['metrics']['playability'] = self._calculate_playability(generated_chart)
        
        # 6. 多样性评分
        results['metrics']['diversity'] = self._calculate_diversity(generated_chart)
        
        return results
    
    def evaluate_batch(self, audio_files: List[str], difficulties: List[int]) -> Dict[str, Any]:
        """
        批量评估多个音频文件
        
        Args:
            audio_files: 音频文件列表
            difficulties: 对应的难度列表
            
        Returns:
            Dict: 批量评估结果
        """
        assert len(audio_files) == len(difficulties), "音频文件数量与难度数量不匹配"
        
        results = []
        for audio_path, difficulty in zip(audio_files, difficulties):
            try:
                result = self.evaluate_single_audio(audio_path, difficulty)
                results.append(result)
            except Exception as e:
                logger.error(f"评估失败 {audio_path}: {e}")
                continue
        
        # 汇总统计
        summary = self._summarize_results(results)
        
        return {
            'individual_results': results,
            'summary': summary
        }
    
    def _calculate_note_density(self, chart: np.ndarray) -> Dict[str, float]:
        """计算音符密度"""
        total_steps = chart.shape[0]
        total_tracks = chart.shape[1]
        
        # 统计各类音符
        empty_notes = np.sum(chart == 0)
        short_notes = np.sum(chart == 1)
        long_notes = np.sum(chart == 2)
        
        total_notes = short_notes + long_notes
        
        return {
            'overall_density': total_notes / (total_steps * total_tracks),
            'short_note_ratio': short_notes / total_notes if total_notes > 0 else 0,
            'long_note_ratio': long_notes / total_notes if total_notes > 0 else 0,
            'notes_per_second': total_notes / (total_steps * 0.125),  # 假设每步0.125秒
        }
    
    def _estimate_difficulty(self, chart: np.ndarray) -> Dict[str, float]:
        """估算谱面难度"""
        # 基于多个因素估算难度
        
        # 1. 音符密度
        density = np.sum(chart > 0) / chart.size
        
        # 2. 同时音符数量
        simultaneous_notes = np.sum(np.sum(chart > 0, axis=1) > 1)
        
        # 3. 快速连击
        rapid_sequences = 0
        for track in range(chart.shape[1]):
            track_notes = chart[:, track]
            for i in range(len(track_notes) - 2):
                if np.sum(track_notes[i:i+3] > 0) >= 2:
                    rapid_sequences += 1
        
        # 4. 长音符复杂度
        long_note_complexity = np.sum(chart == 2) / chart.size
        
        # 综合难度评分 (1-10)
        difficulty_score = (
            density * 3 +
            (simultaneous_notes / chart.shape[0]) * 2 +
            (rapid_sequences / chart.shape[0]) * 3 +
            long_note_complexity * 2
        ) * 10
        
        difficulty_score = min(10, max(1, difficulty_score))
        
        return {
            'estimated_difficulty': difficulty_score,
            'note_density_factor': density,
            'simultaneous_factor': simultaneous_notes / chart.shape[0],
            'rapid_sequence_factor': rapid_sequences / chart.shape[0],
            'long_note_factor': long_note_complexity
        }
    
    def _calculate_music_alignment(self, chart: np.ndarray, audio: np.ndarray, sr: int) -> Dict[str, float]:
        """计算音乐匹配度"""
        # 提取音频节拍
        tempo, beats = librosa.beat.beat_track(y=audio, sr=sr)
        
        # 将节拍转换为时间步
        beat_times = librosa.frames_to_time(beats, sr=sr)
        time_step = 0.125  # 假设每步0.125秒
        beat_steps = (beat_times / time_step).astype(int)
        beat_steps = beat_steps[beat_steps < chart.shape[0]]
        
        # 计算音符在节拍上的分布
        notes_on_beats = 0
        total_notes = 0
        
        for step in range(chart.shape[0]):
            step_notes = np.sum(chart[step] > 0)
            total_notes += step_notes
            
            if step in beat_steps:
                notes_on_beats += step_notes
        
        # 计算匹配度
        beat_alignment = notes_on_beats / total_notes if total_notes > 0 else 0
        
        # 计算节拍强度与音符密度的相关性
        onset_strength = librosa.onset.onset_strength(y=audio, sr=sr)
        onset_times = librosa.frames_to_time(np.arange(len(onset_strength)), sr=sr)
        
        # 重采样到谱面时间步
        chart_times = np.arange(chart.shape[0]) * time_step
        onset_resampled = np.interp(chart_times, onset_times, onset_strength)
        chart_density = np.sum(chart, axis=1)
        
        # 计算相关性
        if len(onset_resampled) == len(chart_density) and np.std(onset_resampled) > 0:
            correlation, _ = pearsonr(onset_resampled, chart_density)
        else:
            correlation = 0
        
        return {
            'beat_alignment': beat_alignment,
            'onset_correlation': correlation,
            'tempo': tempo
        }
    
    def _calculate_rhythm_consistency(self, chart: np.ndarray, audio: np.ndarray, sr: int) -> Dict[str, float]:
        """计算节拍一致性"""
        # 分析谱面的节拍模式
        note_intervals = []
        
        for track in range(chart.shape[1]):
            track_notes = np.where(chart[:, track] > 0)[0]
            if len(track_notes) > 1:
                intervals = np.diff(track_notes)
                note_intervals.extend(intervals)
        
        if not note_intervals:
            return {'consistency_score': 0, 'interval_variance': 0}
        
        # 计算间隔的一致性
        intervals = np.array(note_intervals)
        consistency_score = 1 / (1 + np.var(intervals))  # 方差越小，一致性越高
        
        return {
            'consistency_score': consistency_score,
            'interval_variance': np.var(intervals),
            'mean_interval': np.mean(intervals)
        }
    
    def _calculate_playability(self, chart: np.ndarray) -> Dict[str, float]:
        """计算可玩性评分"""
        # 检查不合理的模式
        
        # 1. 检查过于密集的音符
        dense_sections = 0
        window_size = 8  # 检查8步窗口
        
        for i in range(chart.shape[0] - window_size):
            window = chart[i:i+window_size]
            if np.sum(window > 0) > window_size * 0.8:  # 80%以上都有音符
                dense_sections += 1
        
        # 2. 检查不可能的手指移动
        impossible_moves = 0
        for i in range(chart.shape[0] - 1):
            current_notes = np.where(chart[i] > 0)[0]
            next_notes = np.where(chart[i+1] > 0)[0]
            
            # 检查是否需要同时按下相距太远的键
            if len(current_notes) > 1:
                max_span = np.max(current_notes) - np.min(current_notes)
                if max_span >= 3:  # 跨度超过3个轨道
                    impossible_moves += 1
        
        # 3. 计算整体可玩性
        total_steps = chart.shape[0]
        playability_score = 1 - (dense_sections + impossible_moves) / total_steps
        playability_score = max(0, min(1, playability_score))
        
        return {
            'playability_score': playability_score,
            'dense_sections_ratio': dense_sections / total_steps,
            'impossible_moves_ratio': impossible_moves / total_steps
        }
    
    def _calculate_diversity(self, chart: np.ndarray) -> Dict[str, float]:
        """计算多样性评分"""
        # 分析模式多样性
        
        # 1. 轨道使用均匀性
        track_usage = np.sum(chart > 0, axis=0)
        track_uniformity = 1 - np.std(track_usage) / np.mean(track_usage) if np.mean(track_usage) > 0 else 0
        
        # 2. 音符类型多样性
        note_types = [np.sum(chart == i) for i in range(3)]
        total_notes = sum(note_types[1:])  # 排除空音符
        if total_notes > 0:
            type_entropy = -sum([(count/total_notes) * np.log2(count/total_notes + 1e-8) 
                                for count in note_types[1:] if count > 0])
        else:
            type_entropy = 0
        
        # 3. 时间模式多样性
        pattern_diversity = self._calculate_pattern_diversity(chart)
        
        return {
            'track_uniformity': track_uniformity,
            'type_entropy': type_entropy,
            'pattern_diversity': pattern_diversity,
            'overall_diversity': (track_uniformity + type_entropy + pattern_diversity) / 3
        }
    
    def _calculate_pattern_diversity(self, chart: np.ndarray) -> float:
        """计算模式多样性"""
        # 提取4步模式
        patterns = []
        pattern_size = 4
        
        for i in range(chart.shape[0] - pattern_size + 1):
            pattern = chart[i:i+pattern_size].flatten()
            patterns.append(tuple(pattern))
        
        # 计算唯一模式的比例
        unique_patterns = len(set(patterns))
        total_patterns = len(patterns)
        
        return unique_patterns / total_patterns if total_patterns > 0 else 0
    
    def _summarize_results(self, results: List[Dict[str, Any]]) -> Dict[str, Any]:
        """汇总评估结果"""
        if not results:
            return {}
        
        # 提取所有指标
        all_metrics = {}
        for result in results:
            for metric_category, metrics in result['metrics'].items():
                if metric_category not in all_metrics:
                    all_metrics[metric_category] = {}
                
                if isinstance(metrics, dict):
                    for metric_name, value in metrics.items():
                        if metric_name not in all_metrics[metric_category]:
                            all_metrics[metric_category][metric_name] = []
                        all_metrics[metric_category][metric_name].append(value)
                else:
                    if metric_category not in all_metrics:
                        all_metrics[metric_category] = []
                    all_metrics[metric_category].append(metrics)
        
        # 计算统计信息
        summary = {}
        for category, metrics in all_metrics.items():
            summary[category] = {}
            if isinstance(metrics, dict):
                for metric_name, values in metrics.items():
                    summary[category][metric_name] = {
                        'mean': np.mean(values),
                        'std': np.std(values),
                        'min': np.min(values),
                        'max': np.max(values)
                    }
            else:
                summary[category] = {
                    'mean': np.mean(metrics),
                    'std': np.std(metrics),
                    'min': np.min(metrics),
                    'max': np.max(metrics)
                }
        
        return summary
    
    def generate_evaluation_report(self, results: Dict[str, Any], output_path: str):
        """生成评估报告"""
        # 创建可视化报告
        fig, axes = plt.subplots(2, 3, figsize=(15, 10))
        fig.suptitle('GAN模型评估报告', fontsize=16)
        
        # 这里可以添加各种图表
        # 由于篇幅限制，这里只是一个框架
        
        plt.tight_layout()
        plt.savefig(output_path)
        plt.close()
        
        logger.info(f"评估报告已保存: {output_path}")
