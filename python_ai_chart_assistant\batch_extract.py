#!/usr/bin/env python3
"""
批量解压谱面文件夹中的压缩包

支持的格式：
- .mcz (Malody Chart Zip)
- .zip
- .rar
- .7z
- .tar.gz
- .tar.bz2
"""

import os
import sys
import zipfile
import tarfile
import logging
from pathlib import Path
from typing import List, Optional
import shutil

# 设置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('batch_extract.log', encoding='utf-8'),
        logging.StreamHandler(sys.stdout)
    ]
)
logger = logging.getLogger(__name__)

class BatchExtractor:
    """批量解压器"""
    
    def __init__(self, source_dir: str, extract_dir: str = None):
        """
        初始化批量解压器
        
        Args:
            source_dir: 源文件夹路径
            extract_dir: 解压目标文件夹路径，默认为源文件夹下的"解压"文件夹
        """
        self.source_dir = Path(source_dir)
        self.extract_dir = Path(extract_dir) if extract_dir else self.source_dir / "解压"
        
        # 支持的压缩格式
        self.supported_extensions = {
            '.mcz': self._extract_zip,
            '.zip': self._extract_zip,
            '.rar': self._extract_rar,
            '.7z': self._extract_7z,
            '.tar.gz': self._extract_tar,
            '.tar.bz2': self._extract_tar,
            '.tgz': self._extract_tar,
            '.tbz2': self._extract_tar
        }
        
        # 统计信息
        self.stats = {
            'total_files': 0,
            'processed': 0,
            'success': 0,
            'failed': 0,
            'skipped': 0
        }
    
    def find_archives(self) -> List[Path]:
        """
        查找所有压缩文件
        
        Returns:
            List[Path]: 压缩文件路径列表
        """
        archives = []
        
        if not self.source_dir.exists():
            logger.error(f"源文件夹不存在: {self.source_dir}")
            return archives
        
        logger.info(f"🔍 扫描文件夹: {self.source_dir}")
        
        for file_path in self.source_dir.iterdir():
            if file_path.is_file():
                # 跳过正在下载的文件
                if '.downloading' in file_path.name:
                    logger.debug(f"跳过下载中的文件: {file_path.name}")
                    continue
                
                # 检查文件扩展名
                for ext in self.supported_extensions:
                    if file_path.name.lower().endswith(ext.lower()):
                        archives.append(file_path)
                        break
        
        logger.info(f"✅ 找到 {len(archives)} 个压缩文件")
        return archives
    
    def _extract_zip(self, archive_path: Path, target_dir: Path) -> bool:
        """
        解压ZIP格式文件（包括.mcz）
        
        Args:
            archive_path: 压缩文件路径
            target_dir: 解压目标路径
            
        Returns:
            bool: 是否成功
        """
        try:
            with zipfile.ZipFile(archive_path, 'r') as zip_ref:
                # 获取压缩包内的文件列表
                file_list = zip_ref.namelist()
                logger.debug(f"压缩包包含 {len(file_list)} 个文件")
                
                # 解压所有文件
                zip_ref.extractall(target_dir)
                
                logger.info(f"✅ ZIP解压成功: {len(file_list)} 个文件")
                return True
                
        except zipfile.BadZipFile:
            logger.error(f"❌ 损坏的ZIP文件: {archive_path}")
            return False
        except Exception as e:
            logger.error(f"❌ ZIP解压失败: {e}")
            return False
    
    def _extract_rar(self, archive_path: Path, target_dir: Path) -> bool:
        """
        解压RAR格式文件
        
        Args:
            archive_path: 压缩文件路径
            target_dir: 解压目标路径
            
        Returns:
            bool: 是否成功
        """
        try:
            # 尝试使用rarfile库
            import rarfile
            with rarfile.RarFile(archive_path) as rar_ref:
                rar_ref.extractall(target_dir)
                logger.info(f"✅ RAR解压成功")
                return True
        except ImportError:
            logger.warning("⚠️ 未安装rarfile库，尝试使用系统命令")
            # 尝试使用系统的unrar命令
            import subprocess
            try:
                result = subprocess.run([
                    'unrar', 'x', str(archive_path), str(target_dir)
                ], capture_output=True, text=True)
                
                if result.returncode == 0:
                    logger.info(f"✅ RAR解压成功（系统命令）")
                    return True
                else:
                    logger.error(f"❌ RAR解压失败: {result.stderr}")
                    return False
            except FileNotFoundError:
                logger.error("❌ 未找到unrar命令，请安装WinRAR或rarfile库")
                return False
        except Exception as e:
            logger.error(f"❌ RAR解压失败: {e}")
            return False
    
    def _extract_7z(self, archive_path: Path, target_dir: Path) -> bool:
        """
        解压7Z格式文件
        
        Args:
            archive_path: 压缩文件路径
            target_dir: 解压目标路径
            
        Returns:
            bool: 是否成功
        """
        try:
            # 尝试使用py7zr库
            import py7zr
            with py7zr.SevenZipFile(archive_path, mode='r') as z:
                z.extractall(target_dir)
                logger.info(f"✅ 7Z解压成功")
                return True
        except ImportError:
            logger.warning("⚠️ 未安装py7zr库，尝试使用系统命令")
            # 尝试使用系统的7z命令
            import subprocess
            try:
                result = subprocess.run([
                    '7z', 'x', str(archive_path), f'-o{target_dir}'
                ], capture_output=True, text=True)
                
                if result.returncode == 0:
                    logger.info(f"✅ 7Z解压成功（系统命令）")
                    return True
                else:
                    logger.error(f"❌ 7Z解压失败: {result.stderr}")
                    return False
            except FileNotFoundError:
                logger.error("❌ 未找到7z命令，请安装7-Zip或py7zr库")
                return False
        except Exception as e:
            logger.error(f"❌ 7Z解压失败: {e}")
            return False
    
    def _extract_tar(self, archive_path: Path, target_dir: Path) -> bool:
        """
        解压TAR格式文件
        
        Args:
            archive_path: 压缩文件路径
            target_dir: 解压目标路径
            
        Returns:
            bool: 是否成功
        """
        try:
            with tarfile.open(archive_path, 'r:*') as tar_ref:
                tar_ref.extractall(target_dir)
                logger.info(f"✅ TAR解压成功")
                return True
        except Exception as e:
            logger.error(f"❌ TAR解压失败: {e}")
            return False
    
    def extract_single(self, archive_path: Path) -> bool:
        """
        解压单个文件
        
        Args:
            archive_path: 压缩文件路径
            
        Returns:
            bool: 是否成功
        """
        # 确定解压方法
        extract_method = None
        for ext, method in self.supported_extensions.items():
            if archive_path.name.lower().endswith(ext.lower()):
                extract_method = method
                break
        
        if not extract_method:
            logger.warning(f"⚠️ 不支持的文件格式: {archive_path}")
            self.stats['skipped'] += 1
            return False
        
        # 创建目标文件夹
        archive_name = archive_path.stem
        target_dir = self.extract_dir / archive_name
        
        # 如果目标文件夹已存在，询问是否覆盖
        if target_dir.exists():
            logger.info(f"📁 目标文件夹已存在: {target_dir}")
            response = input(f"是否覆盖 {archive_name}? (y/n/a=全部覆盖/s=跳过): ").lower()
            
            if response == 'n':
                logger.info(f"⏭️ 跳过: {archive_name}")
                self.stats['skipped'] += 1
                return False
            elif response == 's':
                logger.info(f"⏭️ 跳过: {archive_name}")
                self.stats['skipped'] += 1
                return False
            elif response == 'a':
                # 设置全局覆盖标志
                self._overwrite_all = True
            
            # 删除现有文件夹
            shutil.rmtree(target_dir)
        
        # 创建目标文件夹
        target_dir.mkdir(parents=True, exist_ok=True)
        
        logger.info(f"📦 正在解压: {archive_path.name} -> {target_dir}")
        
        # 执行解压
        success = extract_method(archive_path, target_dir)
        
        if success:
            self.stats['success'] += 1
            logger.info(f"✅ 解压完成: {archive_name}")
        else:
            self.stats['failed'] += 1
            logger.error(f"❌ 解压失败: {archive_name}")
        
        self.stats['processed'] += 1
        return success
    
    def extract_all(self, interactive: bool = True) -> dict:
        """
        批量解压所有文件
        
        Args:
            interactive: 是否交互模式
            
        Returns:
            dict: 统计信息
        """
        # 查找所有压缩文件
        archives = self.find_archives()
        self.stats['total_files'] = len(archives)
        
        if not archives:
            logger.warning("⚠️ 没有找到可解压的文件")
            return self.stats
        
        # 创建解压目录
        self.extract_dir.mkdir(parents=True, exist_ok=True)
        logger.info(f"📁 解压目录: {self.extract_dir}")
        
        # 初始化覆盖标志
        self._overwrite_all = False
        
        # 批量解压
        logger.info(f"🚀 开始批量解压 {len(archives)} 个文件...")
        
        for i, archive_path in enumerate(archives, 1):
            logger.info(f"📦 [{i}/{len(archives)}] 处理: {archive_path.name}")
            
            try:
                self.extract_single(archive_path)
            except KeyboardInterrupt:
                logger.info("⏹️ 用户中断操作")
                break
            except Exception as e:
                logger.error(f"❌ 处理文件时出错: {e}")
                self.stats['failed'] += 1
                self.stats['processed'] += 1
        
        # 输出统计信息
        self._print_summary()
        return self.stats
    
    def _print_summary(self):
        """打印统计摘要"""
        logger.info("=" * 50)
        logger.info("📊 批量解压完成！")
        logger.info(f"📁 解压目录: {self.extract_dir}")
        logger.info(f"📦 总文件数: {self.stats['total_files']}")
        logger.info(f"✅ 成功解压: {self.stats['success']}")
        logger.info(f"❌ 解压失败: {self.stats['failed']}")
        logger.info(f"⏭️ 跳过文件: {self.stats['skipped']}")
        logger.info(f"📈 成功率: {self.stats['success']}/{self.stats['processed']} ({self.stats['success']/max(1,self.stats['processed'])*100:.1f}%)")
        logger.info("=" * 50)


def main():
    """主函数"""
    print("🎵 批量解压谱面文件")
    print("=" * 50)
    
    # 默认路径
    default_source = "谱面文件"
    
    # 获取源文件夹路径
    source_dir = input(f"请输入源文件夹路径 (默认: {default_source}): ").strip()
    if not source_dir:
        source_dir = default_source
    
    # 创建解压器
    extractor = BatchExtractor(source_dir)
    
    # 开始批量解压
    try:
        stats = extractor.extract_all()
        
        if stats['success'] > 0:
            print(f"\n🎉 解压完成！成功解压 {stats['success']} 个文件")
            print(f"📁 解压文件位于: {extractor.extract_dir}")
        else:
            print("\n⚠️ 没有成功解压任何文件")
            
    except Exception as e:
        logger.error(f"❌ 批量解压过程中出错: {e}")
        return 1
    
    return 0


if __name__ == "__main__":
    sys.exit(main())
