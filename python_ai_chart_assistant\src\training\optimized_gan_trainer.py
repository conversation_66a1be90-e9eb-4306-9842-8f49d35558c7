"""
优化的GAN训练器
支持WGAN-GP、混合精度训练、高级采样策略等
"""

import torch
import torch.nn as nn
import torch.optim as optim
from torch.utils.tensorboard import SummaryWriter
from torch.cuda.amp import GradScaler, autocast
import yaml
import numpy as np
from pathlib import Path
from tqdm import tqdm
import logging
from typing import Dict, Any, Optional

from ..models.audio_chart_gan import AudioChartGAN
from .audio_chart_dataset import AudioChartDataset
from torch.utils.data import DataLoader

logger = logging.getLogger(__name__)


class OptimizedGANTrainer:
    """优化的GAN训练器"""
    
    def __init__(self, config_path: str, device: str = "cuda"):
        """
        初始化训练器

        Args:
            config_path: 配置文件路径
            device: 训练设备
        """
        print("🚀 开始初始化训练器...")
        self.device = device

        print("📋 加载配置文件...")
        self.config = self._load_config(config_path)

        print("🧠 创建GAN模型...")
        print(f"   - 轨道数: {self.config['model']['track_count']}")
        print(f"   - 最大长度: {self.config['model']['max_length']}")
        print(f"   - 使用WGAN: {self.config['model']['use_wgan']}")

        # 初始化模型
        self.model = AudioChartGAN(
            track_count=self.config['model']['track_count'],
            max_length=self.config['model']['max_length'],
            use_wgan=self.config['model']['use_wgan']
        )

        print(f"📱 将模型移动到设备: {device}")
        self.model = self.model.to(device)

        # 计算模型参数数量
        total_params = sum(p.numel() for p in self.model.parameters())
        trainable_params = sum(p.numel() for p in self.model.parameters() if p.requires_grad)
        print(f"   - 总参数数: {total_params:,}")
        print(f"   - 可训练参数: {trainable_params:,}")

        # 设置损失权重
        self.model.loss_weights = self.config['loss_weights']
        print("⚖️  设置损失权重...")

        print("🔧 初始化优化器...")
        # 初始化优化器
        self._setup_optimizers()

        print("📈 初始化学习率调度器...")
        # 初始化学习率调度器
        self._setup_schedulers()

        # 混合精度训练
        self.use_amp = self.config['device']['mixed_precision']
        if self.use_amp:
            print("⚡ 启用混合精度训练...")
            self.scaler = GradScaler()
        else:
            print("🔢 使用标准精度训练...")

        # 训练状态
        self.current_epoch = 0
        self.best_loss = float('inf')
        self.training_history = []

        # TensorBoard
        self.writer = None
        if self.config['logging']['tensorboard']:
            log_dir = Path(self.config['logging']['tensorboard']['log_dir'])
            print(f"📊 初始化TensorBoard: {log_dir}")
            self.writer = SummaryWriter(log_dir)

        print("✅ 训练器初始化完成！")
    
    def _load_config(self, config_path: str) -> Dict[str, Any]:
        """加载配置文件"""
        with open(config_path, 'r', encoding='utf-8') as f:
            return yaml.safe_load(f)
    
    def _setup_optimizers(self):
        """设置优化器"""
        # 生成器优化器
        self.g_optimizer = optim.Adam(
            self.model.generator.parameters(),
            lr=self.config['training']['generator_lr'],
            betas=(self.config['training']['beta1'], self.config['training']['beta2']),
            weight_decay=self.config['training']['weight_decay']
        )
        
        # 判别器优化器
        self.d_optimizer = optim.Adam(
            self.model.discriminator.parameters(),
            lr=self.config['training']['discriminator_lr'],
            betas=(self.config['training']['beta1'], self.config['training']['beta2']),
            weight_decay=self.config['training']['weight_decay']
        )
    
    def _setup_schedulers(self):
        """设置学习率调度器"""
        scheduler_config = self.config['training']['scheduler']
        
        if scheduler_config['type'] == 'reduce_on_plateau':
            self.g_scheduler = optim.lr_scheduler.ReduceLROnPlateau(
                self.g_optimizer,
                patience=scheduler_config['patience'],
                factor=scheduler_config['factor'],
                min_lr=scheduler_config['min_lr']
            )
            self.d_scheduler = optim.lr_scheduler.ReduceLROnPlateau(
                self.d_optimizer,
                patience=scheduler_config['patience'],
                factor=scheduler_config['factor'],
                min_lr=scheduler_config['min_lr']
            )
    
    def train(self, train_loader: DataLoader, val_loader: DataLoader, 
              num_epochs: int, save_dir: str):
        """
        训练模型
        
        Args:
            train_loader: 训练数据加载器
            val_loader: 验证数据加载器
            num_epochs: 训练轮数
            save_dir: 保存目录
        """
        save_dir = Path(save_dir)
        save_dir.mkdir(parents=True, exist_ok=True)
        
        print(f"\n🎯 开始训练，共{num_epochs}轮")
        print(f"💾 模型保存目录: {save_dir}")

        for epoch in range(num_epochs):
            self.current_epoch = epoch

            print(f"\n📅 Epoch {epoch+1}/{num_epochs}")
            print("-" * 50)

            # 训练阶段
            print("🏃 训练阶段...")
            train_losses = self._train_epoch(train_loader)
            
            # 验证阶段
            if epoch % self.config['validation']['frequency'] == 0:
                print("🔍 验证阶段...")
                val_losses = self._validate_epoch(val_loader)

                # 学习率调度
                avg_val_loss = (val_losses['g_loss'] + val_losses['d_loss']) / 2
                self.g_scheduler.step(avg_val_loss)
                self.d_scheduler.step(avg_val_loss)

                # 记录最佳模型
                if avg_val_loss < self.best_loss:
                    self.best_loss = avg_val_loss
                    self._save_model(save_dir / 'best_model.pth')
                    print(f"🏆 保存最佳模型！验证损失: {avg_val_loss:.4f}")
            else:
                val_losses = {'g_loss': 0, 'd_loss': 0}
            
            # 记录训练历史
            epoch_info = {
                'epoch': epoch,
                'train_g_loss': train_losses['g_loss'],
                'train_d_loss': train_losses['d_loss'],
                'val_g_loss': val_losses['g_loss'],
                'val_d_loss': val_losses['d_loss'],
                'g_lr': self.g_optimizer.param_groups[0]['lr'],
                'd_lr': self.d_optimizer.param_groups[0]['lr']
            }
            self.training_history.append(epoch_info)
            
            # TensorBoard记录
            if self.writer:
                self._log_to_tensorboard(epoch_info, train_losses)
            
            # 定期保存
            if epoch % self.config['save']['frequency'] == 0:
                self._save_checkpoint(save_dir / f'checkpoint_epoch_{epoch}.pth')
            
            # 详细的损失显示
            print(f"📊 训练损失:")
            print(f"   - 生成器: {train_losses['g_loss']:.4f}")
            print(f"   - 判别器: {train_losses['d_loss']:.4f}")
            if epoch % self.config['validation']['frequency'] == 0:
                print(f"📈 验证损失:")
                print(f"   - 生成器: {val_losses['g_loss']:.4f}")
                print(f"   - 判别器: {val_losses['d_loss']:.4f}")
            print(f"🎛️  学习率:")
            print(f"   - 生成器: {epoch_info['g_lr']:.6f}")
            print(f"   - 判别器: {epoch_info['d_lr']:.6f}")
            print(f"⏱️  Epoch {epoch+1} 完成")
        
        # 保存最终模型
        self._save_model(save_dir / 'final_model.pth')
        print(f"\n🎉 训练完成！")
        print(f"🏆 最佳验证损失: {self.best_loss:.4f}")
        print(f"💾 模型已保存到: {save_dir}")
    
    def _train_epoch(self, train_loader: DataLoader) -> Dict[str, float]:
        """训练一个epoch"""
        self.model.train()
        
        total_g_loss = 0.0
        total_d_loss = 0.0
        total_music_loss = 0.0
        num_batches = 0
        
        progress_bar = tqdm(train_loader, desc=f"🏃 训练 Epoch {self.current_epoch+1}")

        for batch_idx, batch in enumerate(progress_bar):
            # 准备数据
            audio_features = batch['audio_features'].to(self.device)
            real_charts = batch['chart'].to(self.device)
            difficulties = batch['difficulty'].to(self.device)
            qualities = batch['quality'].to(self.device)
            
            # 混合精度训练
            if self.use_amp:
                with autocast():
                    losses, d_loss, g_loss = self.model.train_step(
                        real_charts, audio_features, difficulties, qualities
                    )
            else:
                losses, d_loss, g_loss = self.model.train_step(
                    real_charts, audio_features, difficulties, qualities
                )
            
            # 训练判别器
            self.d_optimizer.zero_grad()
            if self.use_amp:
                self.scaler.scale(d_loss).backward(retain_graph=True)
                self.scaler.unscale_(self.d_optimizer)
                torch.nn.utils.clip_grad_norm_(
                    self.model.discriminator.parameters(), 
                    self.config['training']['grad_clip']
                )
                self.scaler.step(self.d_optimizer)
            else:
                d_loss.backward(retain_graph=True)
                torch.nn.utils.clip_grad_norm_(
                    self.model.discriminator.parameters(), 
                    self.config['training']['grad_clip']
                )
                self.d_optimizer.step()
            
            # 训练生成器（根据配置的频率）
            if num_batches % self.config['training']['g_steps'] == 0:
                self.g_optimizer.zero_grad()
                if self.use_amp:
                    self.scaler.scale(g_loss).backward()
                    self.scaler.unscale_(self.g_optimizer)
                    torch.nn.utils.clip_grad_norm_(
                        self.model.generator.parameters(), 
                        self.config['training']['grad_clip']
                    )
                    self.scaler.step(self.g_optimizer)
                    self.scaler.update()
                else:
                    g_loss.backward()
                    torch.nn.utils.clip_grad_norm_(
                        self.model.generator.parameters(), 
                        self.config['training']['grad_clip']
                    )
                    self.g_optimizer.step()
            
            # 累计损失
            total_g_loss += losses['total_generator_loss']
            total_d_loss += losses['discriminator_loss']
            total_music_loss += losses['music_matching_loss']
            num_batches += 1
            
            # 更新进度条
            progress_bar.set_postfix({
                'G': f"{losses['total_generator_loss']:.3f}",
                'D': f"{losses['discriminator_loss']:.3f}",
                'M': f"{losses['music_matching_loss']:.3f}"
            })
        
        return {
            'g_loss': total_g_loss / num_batches,
            'd_loss': total_d_loss / num_batches,
            'music_loss': total_music_loss / num_batches
        }
    
    def _validate_epoch(self, val_loader: DataLoader) -> Dict[str, float]:
        """验证一个epoch"""
        self.model.eval()
        
        total_g_loss = 0.0
        total_d_loss = 0.0
        num_batches = 0
        
        with torch.no_grad():
            for batch in val_loader:
                audio_features = batch['audio_features'].to(self.device)
                real_charts = batch['chart'].to(self.device)
                difficulties = batch['difficulty'].to(self.device)
                qualities = batch['quality'].to(self.device)
                
                losses, d_loss, g_loss = self.model.train_step(
                    real_charts, audio_features, difficulties, qualities
                )
                
                total_g_loss += losses['total_generator_loss']
                total_d_loss += losses['discriminator_loss']
                num_batches += 1
        
        return {
            'g_loss': total_g_loss / num_batches if num_batches > 0 else 0.0,
            'd_loss': total_d_loss / num_batches if num_batches > 0 else 0.0
        }
    
    def _log_to_tensorboard(self, epoch_info: Dict[str, Any], train_losses: Dict[str, float]):
        """记录到TensorBoard"""
        epoch = epoch_info['epoch']
        
        # 损失
        self.writer.add_scalar('Loss/Train_Generator', train_losses['g_loss'], epoch)
        self.writer.add_scalar('Loss/Train_Discriminator', train_losses['d_loss'], epoch)
        self.writer.add_scalar('Loss/Train_Music_Matching', train_losses['music_loss'], epoch)
        
        if epoch_info['val_g_loss'] > 0:
            self.writer.add_scalar('Loss/Val_Generator', epoch_info['val_g_loss'], epoch)
            self.writer.add_scalar('Loss/Val_Discriminator', epoch_info['val_d_loss'], epoch)
        
        # 学习率
        self.writer.add_scalar('Learning_Rate/Generator', epoch_info['g_lr'], epoch)
        self.writer.add_scalar('Learning_Rate/Discriminator', epoch_info['d_lr'], epoch)
    
    def _save_model(self, path: str):
        """保存模型"""
        torch.save({
            'model_state_dict': self.model.state_dict(),
            'config': self.config
        }, path)
    
    def _save_checkpoint(self, path: str):
        """保存训练检查点"""
        checkpoint = {
            'epoch': self.current_epoch,
            'model_state_dict': self.model.state_dict(),
            'g_optimizer_state_dict': self.g_optimizer.state_dict(),
            'd_optimizer_state_dict': self.d_optimizer.state_dict(),
            'g_scheduler_state_dict': self.g_scheduler.state_dict(),
            'd_scheduler_state_dict': self.d_scheduler.state_dict(),
            'best_loss': self.best_loss,
            'training_history': self.training_history,
            'config': self.config
        }
        
        if self.use_amp:
            checkpoint['scaler_state_dict'] = self.scaler.state_dict()
        
        torch.save(checkpoint, path)
        logger.info(f"检查点已保存: {path}")
    
    def load_checkpoint(self, path: str):
        """加载训练检查点"""
        checkpoint = torch.load(path, map_location=self.device)
        
        self.model.load_state_dict(checkpoint['model_state_dict'])
        self.g_optimizer.load_state_dict(checkpoint['g_optimizer_state_dict'])
        self.d_optimizer.load_state_dict(checkpoint['d_optimizer_state_dict'])
        self.g_scheduler.load_state_dict(checkpoint['g_scheduler_state_dict'])
        self.d_scheduler.load_state_dict(checkpoint['d_scheduler_state_dict'])
        
        self.current_epoch = checkpoint['epoch']
        self.best_loss = checkpoint['best_loss']
        self.training_history = checkpoint['training_history']
        
        if self.use_amp and 'scaler_state_dict' in checkpoint:
            self.scaler.load_state_dict(checkpoint['scaler_state_dict'])
        
        logger.info(f"检查点已加载: {path}, epoch: {self.current_epoch}")
