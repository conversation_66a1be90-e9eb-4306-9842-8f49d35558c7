"""
模型训练脚本

训练AI谱面生成模型
"""

import torch
import torch.nn as nn
import torch.optim as optim
from torch.utils.tensorboard import SummaryWriter
import numpy as np
from pathlib import Path
from tqdm import tqdm
import logging
from typing import Dict, Optional

from ..models import ChartGenerationModel, DifficultyPredictor
from ..audio_analysis import FeatureExtractor
from .data_loader import ChartDataLoader
from ..utils.config_manager import ConfigManager
from ..utils.logger_setup import setup_logging

logger = logging.getLogger(__name__)


class ModelTrainer:
    """模型训练器"""
    
    def __init__(
        self,
        config_path: Optional[str] = None,
        device: str = 'cpu'
    ):
        """
        初始化训练器
        
        Args:
            config_path: 配置文件路径
            device: 计算设备
        """
        self.device = device
        
        # 加载配置
        self.config_manager = ConfigManager(config_path)
        self.config = self.config_manager.get_config()
        
        # 设置日志
        setup_logging(**self.config.get('logging', {}))
        
        # 初始化组件
        self.feature_extractor = FeatureExtractor()
        self.data_loader = None
        self.model = None
        self.optimizer = None
        self.scheduler = None
        self.writer = None
        
        # 训练状态
        self.current_epoch = 0
        self.best_loss = float('inf')
        self.training_history = []
        
        logger.info(f"训练器初始化完成，设备: {device}")
    
    def setup_model(self, model_type: str = 'generation'):
        """
        设置模型
        
        Args:
            model_type: 模型类型 ('generation' 或 'difficulty')
        """
        model_config = self.config.get('model', {})
        
        if model_type == 'generation':
            self.model = ChartGenerationModel(
                input_dim=model_config.get('input_dim', 28),
                hidden_dim=model_config.get('hidden_dim', 256),
                num_layers=model_config.get('num_layers', 3),
                dropout=model_config.get('dropout', 0.2)
            )
        elif model_type == 'difficulty':
            self.model = DifficultyPredictor(
                input_dim=model_config.get('input_dim', 28),
                hidden_dim=model_config.get('hidden_dim', 128),
                num_layers=model_config.get('num_layers', 2),
                dropout=model_config.get('dropout', 0.3)
            )
        else:
            raise ValueError(f"不支持的模型类型: {model_type}")
        
        self.model.to(self.device)
        logger.info(f"模型设置完成: {model_type}")
    
    def setup_data_loader(self, data_dir: str):
        """
        设置数据加载器
        
        Args:
            data_dir: 数据目录
        """
        training_config = self.config.get('training', {})
        
        self.data_loader = ChartDataLoader(
            data_dir=data_dir,
            feature_extractor=self.feature_extractor,
            batch_size=training_config.get('batch_size', 32),
            validation_split=training_config.get('validation_split', 0.2)
        )
        
        logger.info("数据加载器设置完成")
    
    def setup_optimizer(self):
        """设置优化器"""
        training_config = self.config.get('training', {})
        
        self.optimizer = optim.Adam(
            self.model.parameters(),
            lr=training_config.get('learning_rate', 0.001),
            weight_decay=training_config.get('weight_decay', 1e-5)
        )
        
        # 学习率调度器
        self.scheduler = optim.lr_scheduler.ReduceLROnPlateau(
            self.optimizer,
            mode='min',
            factor=0.5,
            patience=10,
            verbose=True
        )
        
        logger.info("优化器设置完成")
    
    def setup_tensorboard(self, log_dir: str):
        """设置TensorBoard"""
        self.writer = SummaryWriter(log_dir)
        logger.info(f"TensorBoard日志目录: {log_dir}")
    
    def train_generation_model(self, num_epochs: int, save_dir: str):
        """
        训练谱面生成模型
        
        Args:
            num_epochs: 训练轮数
            save_dir: 模型保存目录
        """
        if not self.model or not self.data_loader:
            raise ValueError("请先设置模型和数据加载器")
        
        save_dir = Path(save_dir)
        save_dir.mkdir(parents=True, exist_ok=True)
        
        train_loader = self.data_loader.get_train_loader()
        val_loader = self.data_loader.get_val_loader()
        
        logger.info(f"开始训练谱面生成模型，共 {num_epochs} 轮")
        
        for epoch in range(num_epochs):
            self.current_epoch = epoch
            
            # 训练阶段
            train_loss = self._train_epoch(train_loader)
            
            # 验证阶段
            val_loss = self._validate_epoch(val_loader)
            
            # 学习率调度
            self.scheduler.step(val_loss)
            
            # 记录训练历史
            epoch_info = {
                'epoch': epoch,
                'train_loss': train_loss,
                'val_loss': val_loss,
                'lr': self.optimizer.param_groups[0]['lr']
            }
            self.training_history.append(epoch_info)
            
            # TensorBoard记录
            if self.writer:
                self.writer.add_scalar('Loss/Train', train_loss, epoch)
                self.writer.add_scalar('Loss/Validation', val_loss, epoch)
                self.writer.add_scalar('Learning_Rate', epoch_info['lr'], epoch)
            
            # 保存最佳模型
            if val_loss < self.best_loss:
                self.best_loss = val_loss
                self._save_model(save_dir / 'best_model.pth')
                logger.info(f"保存最佳模型，验证损失: {val_loss:.4f}")
            
            # 定期保存检查点
            if (epoch + 1) % 10 == 0:
                self._save_checkpoint(save_dir / f'checkpoint_epoch_{epoch+1}.pth')
            
            logger.info(
                f"Epoch {epoch+1}/{num_epochs} - "
                f"Train Loss: {train_loss:.4f}, "
                f"Val Loss: {val_loss:.4f}, "
                f"LR: {epoch_info['lr']:.6f}"
            )
        
        # 保存最终模型
        self._save_model(save_dir / 'final_model.pth')
        logger.info("训练完成")
    
    def _train_epoch(self, train_loader) -> float:
        """训练一个epoch - 支持GAN训练"""
        if hasattr(self.model, 'generator'):
            return self._train_gan_epoch(train_loader)
        else:
            return self._train_standard_epoch(train_loader)

    def _train_standard_epoch(self, train_loader) -> float:
        """标准模型训练"""
        self.model.train()
        total_loss = 0.0
        num_batches = 0

        progress_bar = tqdm(train_loader, desc=f"Training Epoch {self.current_epoch+1}")

        for batch in progress_bar:
            self.optimizer.zero_grad()

            # 前向传播
            features = batch['features'].to(self.device)
            sequences = {k: v.to(self.device) for k, v in batch['sequences'].items()}
            difficulties = batch['difficulties'].to(self.device)

            # 模型预测
            outputs = self.model(features, difficulties)

            # 计算损失
            loss = self.model.calculate_loss(outputs, sequences)

            # 反向传播
            loss.backward()

            # 梯度裁剪
            torch.nn.utils.clip_grad_norm_(self.model.parameters(), max_norm=1.0)

            self.optimizer.step()

            total_loss += loss.item()
            num_batches += 1

            # 更新进度条
            progress_bar.set_postfix({'loss': loss.item()})

        return total_loss / num_batches if num_batches > 0 else 0.0

    def _train_gan_epoch(self, train_loader) -> float:
        """GAN模型训练 - 优化版本"""
        self.model.train()

        # 分别为生成器和判别器创建优化器
        if not hasattr(self, 'g_optimizer'):
            self.g_optimizer = torch.optim.Adam(
                self.model.generator.parameters(),
                lr=self.config.get('generator_lr', 0.0002),
                betas=(0.5, 0.999)
            )
            self.d_optimizer = torch.optim.Adam(
                self.model.discriminator.parameters(),
                lr=self.config.get('discriminator_lr', 0.0002),
                betas=(0.5, 0.999)
            )

        total_g_loss = 0.0
        total_d_loss = 0.0
        num_batches = 0

        progress_bar = tqdm(train_loader, desc=f"GAN Training Epoch {self.current_epoch+1}")

        for batch in progress_bar:
            # 准备数据
            audio_features = batch['features'].to(self.device)
            real_charts = batch['charts'].to(self.device)  # 真实谱面
            difficulties = batch['difficulties'].to(self.device)
            qualities = batch.get('qualities', torch.ones_like(difficulties) * 7).to(self.device)

            # 获取损失
            losses, d_loss, g_loss = self.model.train_step(
                real_charts, audio_features, difficulties, qualities
            )

            # 训练判别器
            self.d_optimizer.zero_grad()
            d_loss.backward(retain_graph=True)
            torch.nn.utils.clip_grad_norm_(self.model.discriminator.parameters(), max_norm=1.0)
            self.d_optimizer.step()

            # 训练生成器（每2步训练一次，避免判别器过强）
            if num_batches % 2 == 0:
                self.g_optimizer.zero_grad()
                g_loss.backward()
                torch.nn.utils.clip_grad_norm_(self.model.generator.parameters(), max_norm=1.0)
                self.g_optimizer.step()

            total_g_loss += losses['total_generator_loss']
            total_d_loss += losses['discriminator_loss']
            num_batches += 1

            # 更新进度条
            progress_bar.set_postfix({
                'G_loss': f"{losses['total_generator_loss']:.4f}",
                'D_loss': f"{losses['discriminator_loss']:.4f}",
                'Music_loss': f"{losses['music_matching_loss']:.4f}"
            })

        avg_loss = (total_g_loss + total_d_loss) / (2 * num_batches) if num_batches > 0 else 0.0
        return avg_loss
    
    def _validate_epoch(self, val_loader) -> float:
        """验证一个epoch"""
        self.model.eval()
        total_loss = 0.0
        num_batches = 0
        
        with torch.no_grad():
            for batch in val_loader:
                features = batch['features'].to(self.device)
                sequences = {k: v.to(self.device) for k, v in batch['sequences'].items()}
                difficulties = batch['difficulties'].to(self.device)
                
                outputs = self.model(features, difficulties)
                loss = self.model.calculate_loss(outputs, sequences)
                
                total_loss += loss.item()
                num_batches += 1
        
        return total_loss / num_batches if num_batches > 0 else 0.0
    
    def _save_model(self, path: str):
        """保存模型"""
        self.model.save_model(path)
    
    def _save_checkpoint(self, path: str):
        """保存训练检查点"""
        checkpoint = {
            'epoch': self.current_epoch,
            'model_state_dict': self.model.state_dict(),
            'optimizer_state_dict': self.optimizer.state_dict(),
            'scheduler_state_dict': self.scheduler.state_dict(),
            'best_loss': self.best_loss,
            'training_history': self.training_history
        }
        
        torch.save(checkpoint, path)
        logger.info(f"检查点已保存: {path}")
    
    def load_checkpoint(self, path: str):
        """加载训练检查点"""
        checkpoint = torch.load(path, map_location=self.device)
        
        self.current_epoch = checkpoint['epoch']
        self.model.load_state_dict(checkpoint['model_state_dict'])
        self.optimizer.load_state_dict(checkpoint['optimizer_state_dict'])
        self.scheduler.load_state_dict(checkpoint['scheduler_state_dict'])
        self.best_loss = checkpoint['best_loss']
        self.training_history = checkpoint['training_history']
        
        logger.info(f"检查点已加载: {path}")
    
    def evaluate_model(self, test_data_dir: str) -> Dict:
        """评估模型性能"""
        if not self.model:
            raise ValueError("请先设置模型")
        
        # 创建测试数据加载器
        test_loader = ChartDataLoader(
            data_dir=test_data_dir,
            feature_extractor=self.feature_extractor,
            batch_size=32,
            validation_split=0.0  # 使用全部数据作为测试集
        ).get_train_loader()  # 实际上是测试数据
        
        self.model.eval()
        total_loss = 0.0
        num_batches = 0
        
        with torch.no_grad():
            for batch in tqdm(test_loader, desc="Evaluating"):
                features = batch['features'].to(self.device)
                sequences = {k: v.to(self.device) for k, v in batch['sequences'].items()}
                difficulties = batch['difficulties'].to(self.device)
                
                outputs = self.model(features, difficulties)
                loss = self.model.calculate_loss(outputs, sequences)
                
                total_loss += loss.item()
                num_batches += 1
        
        avg_loss = total_loss / num_batches if num_batches > 0 else 0.0
        
        return {
            'test_loss': avg_loss,
            'num_samples': len(test_loader.dataset),
            'num_batches': num_batches
        }


def main():
    """主训练函数"""
    import argparse
    
    parser = argparse.ArgumentParser(description='训练AI谱面生成模型')
    parser.add_argument('--config', '-c', help='配置文件路径')
    parser.add_argument('--data-dir', required=True, help='训练数据目录')
    parser.add_argument('--model-type', default='generation', 
                       choices=['generation', 'difficulty'], help='模型类型')
    parser.add_argument('--epochs', type=int, default=100, help='训练轮数')
    parser.add_argument('--save-dir', default='models', help='模型保存目录')
    parser.add_argument('--device', default='cpu', help='计算设备')
    parser.add_argument('--resume', help='恢复训练的检查点路径')
    
    args = parser.parse_args()
    
    try:
        # 初始化训练器
        trainer = ModelTrainer(args.config, args.device)
        
        # 设置模型和数据
        trainer.setup_model(args.model_type)
        trainer.setup_data_loader(args.data_dir)
        trainer.setup_optimizer()
        
        # 设置TensorBoard
        log_dir = Path(args.save_dir) / 'tensorboard_logs'
        trainer.setup_tensorboard(str(log_dir))
        
        # 恢复训练（如果指定）
        if args.resume:
            trainer.load_checkpoint(args.resume)
        
        # 开始训练
        trainer.train_generation_model(args.epochs, args.save_dir)
        
    except KeyboardInterrupt:
        logger.info("训练被用户中断")
    except Exception as e:
        logger.error(f"训练失败: {e}")
        raise


if __name__ == '__main__':
    main()
