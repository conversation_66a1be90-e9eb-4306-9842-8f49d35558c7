"""
格式转换器模块

支持多种音游格式的导入导出
"""

import zipfile
import tempfile
from pathlib import Path
import logging

from .malody_converter import MalodyConverter
from .rhythm_master_converter import RhythmMasterConverter
from .imd_binary_converter import IMDBinaryConverter
from .osu_converter import OsuConverter
from .base_converter import BaseConverter

logger = logging.getLogger(__name__)

# 便捷导入函数
def import_chart_from_file(file_path: str):
    """
    从文件导入谱面数据

    Args:
        file_path: 谱面文件路径

    Returns:
        ChartData: 导入的谱面数据，失败时返回None
    """
    file_path = Path(file_path)
    file_ext = file_path.suffix.lower()

    # 处理.mcz压缩文件
    if file_ext == '.mcz':
        return _import_mcz_file(str(file_path))
    elif file_ext in ['.mc']:
        converter = MalodyConverter()
    elif file_ext == '.imd':
        # 使用专门的IMD二进制转换器
        converter = IMDBinaryConverter()
    elif file_ext in ['.xml']:
        converter = RhythmMasterConverter()
    elif file_ext in ['.osu']:
        converter = OsuConverter()
    else:
        logger.warning(f"不支持的文件格式: {file_ext}")
        return None

    return converter.import_chart(str(file_path))


def _import_mcz_file(mcz_path: str):
    """
    导入.mcz压缩文件

    Args:
        mcz_path: .mcz文件路径

    Returns:
        ChartData: 导入的谱面数据，失败时返回None
    """
    try:
        with tempfile.TemporaryDirectory() as temp_dir:
            # 解压.mcz文件
            with zipfile.ZipFile(mcz_path, 'r') as zip_ref:
                zip_ref.extractall(temp_dir)

            # 查找谱面文件
            temp_path = Path(temp_dir)
            chart_files = []

            # 查找支持的谱面文件
            for ext in ['.mc', '.imd', '.xml', '.osu']:
                chart_files.extend(temp_path.glob(f"*{ext}"))

            if not chart_files:
                logger.error(f"在.mcz文件中未找到支持的谱面文件: {mcz_path}")
                return None

            # 使用第一个找到的谱面文件
            chart_file = chart_files[0]
            logger.info(f"从.mcz文件中提取谱面: {chart_file.name}")

            return import_chart_from_file(str(chart_file))

    except Exception as e:
        logger.error(f"导入.mcz文件失败 {mcz_path}: {e}")
        return None

def get_converter(format_name: str):
    """
    根据格式名称获取转换器

    Args:
        format_name: 格式名称 ('malody', 'rhythm_master', 'osu')

    Returns:
        BaseConverter: 对应的转换器实例
    """
    converters = {
        'malody': MalodyConverter,
        'rhythm_master': RhythmMasterConverter,
        'osu': OsuConverter
    }

    if format_name not in converters:
        raise ValueError(f"不支持的格式: {format_name}")

    return converters[format_name]()

def get_available_converters():
    """
    获取所有可用的转换器

    Returns:
        Dict: 格式名称到转换器类的映射
    """
    return {
        'malody': MalodyConverter,
        'rhythm_master': RhythmMasterConverter,
        'osu': OsuConverter
    }

__all__ = [
    "MalodyConverter",
    "RhythmMasterConverter",
    "OsuConverter",
    "BaseConverter",
    "import_chart_from_file",
    "get_converter",
    "get_available_converters",
]
