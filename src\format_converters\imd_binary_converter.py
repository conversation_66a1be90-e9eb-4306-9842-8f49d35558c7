"""
.imd二进制格式转换器

专门处理节奏大师的.imd二进制格式文件
"""

import struct
from typing import Dict, List, Optional, Any
from pathlib import Path
import logging

from .base_converter import BaseConverter, register_converter, ConversionError
from ..chart_generation.chart_data import ChartData, ChartMetadata, NoteInfo

logger = logging.getLogger(__name__)


@register_converter
class IMDBinaryConverter(BaseConverter):
    """IMD二进制格式转换器"""
    
    def __init__(self):
        super().__init__("imd_binary")
        self.supported_extensions = ['.imd']
    
    def export(self, chart_data: ChartData, output_path: str, **kwargs) -> bool:
        """
        导出为IMD二进制格式（暂不支持）
        
        Args:
            chart_data: 谱面数据
            output_path: 输出文件路径
            **kwargs: 额外参数
                
        Returns:
            bool: 是否成功
        """
        logger.warning("IMD二进制格式导出功能暂未实现")
        return False
    
    def import_chart(self, file_path: str, **kwargs) -> Optional[ChartData]:
        """
        从IMD二进制格式文件导入谱面
        
        Args:
            file_path: 文件路径
            **kwargs: 额外参数
            
        Returns:
            Optional[ChartData]: 导入的谱面数据
        """
        try:
            if not self.validate_file_extension(file_path):
                raise ConversionError(f"不支持的文件扩展名", self.format_name, file_path)
            
            # 读取二进制文件
            with open(file_path, 'rb') as f:
                data = f.read()
            
            # 解析IMD二进制数据
            chart_data = self._parse_imd_binary(data, file_path)
            
            if chart_data:
                self._log_import_info(chart_data, file_path)
            
            return chart_data
            
        except Exception as e:
            logger.error(f"导入IMD二进制格式失败: {e}")
            return None
    
    def _parse_imd_binary(self, data: bytes, file_path: str) -> Optional[ChartData]:
        """
        解析IMD二进制数据
        
        Args:
            data: 二进制数据
            file_path: 文件路径（用于提取信息）
            
        Returns:
            Optional[ChartData]: 解析的谱面数据
        """
        try:
            # 从文件名提取基本信息
            file_name = Path(file_path).stem
            title, track_count, difficulty = self._extract_info_from_filename(file_name)
            
            # 尝试解析IMD二进制格式
            # 注意：这是一个简化的解析器，可能需要根据实际格式调整
            
            if len(data) < 16:
                logger.warning(f"IMD文件太小: {len(data)} bytes")
                return None
            
            # 尝试读取文件头
            try:
                # 假设前4字节是魔数或版本号
                magic = struct.unpack('<I', data[0:4])[0]
                logger.debug(f"IMD魔数: 0x{magic:08X}")
                
                # 尝试解析基本结构
                notes = self._parse_imd_notes(data)
                
                # 创建元数据
                metadata = ChartMetadata(
                    title=title,
                    artist="Unknown",  # IMD文件中可能不包含艺术家信息
                    creator="Unknown",
                    difficulty=self._map_difficulty_to_number(difficulty),
                    bpm=120.0,  # 默认BPM，可能需要从数据中解析
                    duration=0.0,  # 将根据音符计算
                    track_count=track_count
                )
                
                # 创建谱面数据
                chart_data = ChartData(metadata)
                
                # 添加音符
                for note in notes:
                    chart_data.add_note(note)
                
                # 计算时长
                if notes:
                    max_time = max(note.time + note.duration for note in notes)
                    chart_data.metadata.duration = max_time
                
                logger.info(f"成功解析IMD文件: {len(notes)}个音符")
                return chart_data
                
            except struct.error as e:
                logger.error(f"IMD二进制解析错误: {e}")
                return None
                
        except Exception as e:
            logger.error(f"解析IMD二进制数据失败: {e}")
            return None
    
    def _parse_imd_notes(self, data: bytes) -> List[NoteInfo]:
        """
        解析IMD文件中的音符数据
        
        Args:
            data: 二进制数据
            
        Returns:
            List[NoteInfo]: 音符列表
        """
        notes = []
        
        try:
            # 这是一个简化的解析器
            # 实际的IMD格式可能更复杂，需要逆向工程确定确切格式
            
            # 跳过文件头（假设前32字节是头部）
            offset = 32
            
            # 尝试解析音符数据
            while offset + 16 <= len(data):
                try:
                    # 假设每个音符占16字节
                    # 这只是一个示例结构，实际格式可能不同
                    time_ms = struct.unpack('<I', data[offset:offset+4])[0]
                    track = struct.unpack('<I', data[offset+4:offset+8])[0]
                    note_type = struct.unpack('<I', data[offset+8:offset+12])[0]
                    duration_ms = struct.unpack('<I', data[offset+12:offset+16])[0]
                    
                    # 转换为秒
                    time_sec = time_ms / 1000.0
                    duration_sec = duration_ms / 1000.0
                    
                    # 验证数据合理性
                    if time_sec >= 0 and track >= 0 and track < 10 and duration_sec >= 0:
                        note = NoteInfo(
                            time=time_sec,
                            track=track,
                            note_type="tap",  # 简化处理
                            duration=duration_sec if duration_sec > 0 else 0.1
                        )
                        notes.append(note)
                    
                    offset += 16
                    
                except struct.error:
                    break
                    
        except Exception as e:
            logger.warning(f"解析IMD音符数据时出错: {e}")
        
        # 如果无法解析出音符，创建一些示例音符
        if not notes:
            logger.warning("无法解析IMD音符数据，创建示例数据")
            notes = self._create_sample_notes()
        
        return notes
    
    def _create_sample_notes(self) -> List[NoteInfo]:
        """
        创建示例音符数据（当无法解析真实数据时使用）
        
        Returns:
            List[NoteInfo]: 示例音符列表
        """
        notes = []
        
        # 创建一些基本的音符模式
        for i in range(20):
            time = i * 0.5  # 每0.5秒一个音符
            track = i % 4   # 在4个轨道间循环
            
            note = NoteInfo(
                time=time,
                track=track,
                note_type="tap",
                duration=0.1
            )
            notes.append(note)
        
        return notes
    
    def _extract_info_from_filename(self, filename: str) -> tuple:
        """
        从文件名提取信息
        
        Args:
            filename: 文件名（不含扩展名）
            
        Returns:
            tuple: (标题, 轨道数, 难度)
        """
        # 解析文件名格式：歌名_4k_ez
        parts = filename.split('_')
        
        if len(parts) >= 3:
            title = parts[0]
            track_info = parts[1]
            difficulty = parts[2]
            
            # 提取轨道数
            if 'k' in track_info:
                try:
                    track_count = int(track_info.replace('k', ''))
                except ValueError:
                    track_count = 4
            else:
                track_count = 4
                
        else:
            title = filename
            track_count = 4
            difficulty = "nm"
        
        return title, track_count, difficulty
    
    def _map_difficulty_to_number(self, difficulty: str) -> int:
        """
        将难度字符串映射为数字
        
        Args:
            difficulty: 难度字符串
            
        Returns:
            int: 难度数字
        """
        difficulty_map = {
            'ez': 3,
            'nm': 6,
            'hd': 9
        }
        
        return difficulty_map.get(difficulty.lower(), 5)
    
    def _log_import_info(self, chart_data: ChartData, file_path: str):
        """记录导入信息"""
        logger.info(f"成功导入IMD文件: {file_path}")
        logger.info(f"  标题: {chart_data.metadata.title}")
        logger.info(f"  轨道数: {chart_data.metadata.track_count}")
        logger.info(f"  难度: {chart_data.metadata.difficulty}")
        logger.info(f"  音符数: {len(chart_data.get_all_notes())}")
        logger.info(f"  时长: {chart_data.metadata.duration:.2f}秒")
