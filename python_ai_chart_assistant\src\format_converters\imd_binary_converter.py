"""
.imd二进制格式转换器

基于JavaScript转谱工具的逻辑，专门处理节奏大师的.imd二进制格式文件
"""

import struct
from typing import Dict, List, Optional, Any, Tuple
from pathlib import Path
import logging

from .base_converter import BaseConverter, register_converter, ConversionError
from ..chart_generation.chart_data import ChartData, ChartMetadata, NoteInfo

logger = logging.getLogger(__name__)


class BufferReader:
    """二进制数据读取器，模拟JavaScript的BufferReader"""

    def __init__(self, buffer: bytes):
        if not isinstance(buffer, bytes):
            raise TypeError(f"Buffer must be bytes, got {type(buffer)}")
        self.buffer = buffer
        self.position = int(0)  # 确保是整数
        self.length = int(len(buffer))  # 确保是整数

    def read_uint(self, size: int, little_endian: bool = True) -> int:
        """读取无符号整数"""
        if self.position + size > self.length:
            raise struct.error("Buffer overflow")

        data = self.buffer[self.position:self.position + size]
        self.position += size

        if size == 1:
            return struct.unpack('B', data)[0]
        elif size == 2:
            return struct.unpack('<H' if little_endian else '>H', data)[0]
        elif size == 4:
            return struct.unpack('<I' if little_endian else '>I', data)[0]
        else:
            raise ValueError(f"Unsupported size: {size}")

    def read_int(self, size: int, little_endian: bool = True) -> int:
        """读取有符号整数"""
        if self.position + size > self.length:
            raise struct.error("Buffer overflow")

        data = self.buffer[self.position:self.position + size]
        self.position += size

        if size == 1:
            return struct.unpack('b', data)[0]
        elif size == 2:
            return struct.unpack('<h' if little_endian else '>h', data)[0]
        elif size == 4:
            return struct.unpack('<i' if little_endian else '>i', data)[0]
        else:
            raise ValueError(f"Unsupported size: {size}")

    def read_float(self, size: int, little_endian: bool = True) -> float:
        """读取浮点数"""
        if self.position + size > self.length:
            raise struct.error("Buffer overflow")

        data = self.buffer[self.position:self.position + size]
        self.position += size

        if size == 4:
            return struct.unpack('<f' if little_endian else '>f', data)[0]
        elif size == 8:
            return struct.unpack('<d' if little_endian else '>d', data)[0]
        else:
            raise ValueError(f"Unsupported size: {size}")

    def read_text(self, length: int, encoding: str = 'utf-8') -> str:
        """读取文本"""
        if self.position + length > self.length:
            raise struct.error("Buffer overflow")

        data = self.buffer[self.position:self.position + length]
        self.position += length

        # 移除null终止符
        if b'\x00' in data:
            data = data[:data.index(b'\x00')]

        return data.decode(encoding, errors='ignore')

    def eof(self) -> bool:
        """是否到达文件末尾"""
        return self.position >= self.length

    def seek(self, offset: int, whence: int = 0):
        """移动读取位置"""
        if whence == 0:  # 绝对位置
            self.position = offset
        elif whence == 1:  # 相对当前位置
            self.position += offset
        elif whence == 2:  # 相对文件末尾
            self.position = self.length + offset

        self.position = max(0, min(self.position, self.length))


@register_converter
class IMDBinaryConverter(BaseConverter):
    """IMD二进制格式转换器"""
    
    def __init__(self):
        super().__init__("imd_binary")
        self.supported_extensions = ['.imd']
    
    def export(self, chart_data: ChartData, output_path: str, **kwargs) -> bool:
        """
        导出为IMD二进制格式（暂不支持）
        
        Args:
            chart_data: 谱面数据
            output_path: 输出文件路径
            **kwargs: 额外参数
                
        Returns:
            bool: 是否成功
        """
        logger.warning("IMD二进制格式导出功能暂未实现")
        return False
    
    def import_chart(self, file_path: str, **kwargs) -> Optional[ChartData]:
        """
        从IMD二进制格式文件导入谱面
        
        Args:
            file_path: 文件路径
            **kwargs: 额外参数
            
        Returns:
            Optional[ChartData]: 导入的谱面数据
        """
        try:
            if not self.validate_file_extension(file_path):
                raise ConversionError(f"不支持的文件扩展名", self.format_name, file_path)
            
            # 读取二进制文件
            with open(file_path, 'rb') as f:
                data = f.read()
            
            # 解析IMD二进制数据
            chart_data = self._parse_imd_binary(data, file_path)
            
            if chart_data:
                self._log_import_info(chart_data, file_path)
            
            return chart_data
            
        except Exception as e:
            logger.error(f"导入IMD二进制格式失败: {e}")
            return None
    
    def _parse_imd_binary(self, data: bytes, file_path: str) -> Optional[ChartData]:
        """
        解析IMD二进制数据（简化版本）

        Args:
            data: 二进制数据
            file_path: 文件路径（用于提取信息）

        Returns:
            Optional[ChartData]: 解析的谱面数据
        """
        try:
            # 从文件名提取基本信息
            file_name = Path(file_path).stem
            title, track_count, difficulty = self._extract_info_from_filename(file_name)

            logger.info(f"处理IMD文件: {file_name}")
            logger.info(f"提取信息 - 标题: {title}, 轨道数: {track_count}, 难度: {difficulty}")

            # 由于IMD二进制格式复杂，暂时使用基于文件名的示例数据生成
            # 这样可以让训练流程先跑起来，后续可以改进解析逻辑

            # 创建元数据
            metadata = ChartMetadata(
                title=title,
                artist="Unknown",
                creator="RhythmMaster",
                difficulty=self._map_difficulty_to_number(difficulty),
                bpm=120.0,  # 可以根据歌曲调整
                duration=0.0,  # 将根据音符计算
                track_count=track_count
            )
          
            # 创建谱面数据
            chart_data = ChartData(metadata)
            

         
        

            # 确保data是bytes类型
            if not isinstance(data, bytes):
                logger.error(f"数据类型错误: 期望bytes，实际{type(data)}")
                return None

            # 设置当前轨道数，供轨道计算方法使用
            self._current_track_count = track_count

            # 使用直接的二进制解析方法
            notes = self._parse_imd_notes(data)

            # 如果解析失败，直接返回失败
            if not notes:
                logger.error("无法解析IMD音符数据，文件可能损坏或格式不支持")
                return None

            # 添加音符
            for note in notes:
                chart_data.add_note(note)


            # 计算时长
            if notes:
                try:
                    max_time = max(float(note.time) + float(note.duration) for note in notes)
                    chart_data.metadata.duration = max_time
                except (ValueError, TypeError):
                    chart_data.metadata.duration = 60.0  # 默认时长

            logger.info(f"成功创建IMD谱面数据: {len(notes)}个音符, 时长: {chart_data.metadata.duration:.1f}秒")
            return chart_data

        except Exception as e:
            logger.error(f"处理IMD文件失败: {e}")
            return None

    def _parse_imd_notes_with_reader(self, reader: BufferReader) -> List[NoteInfo]:
        """
        使用BufferReader解析IMD二进制数据中的音符

        Args:
            reader: 二进制数据读取器

        Returns:
            List[NoteInfo]: 音符列表
        """
        notes = []

        try:
            # IMD文件格式分析（基于JavaScript转谱工具）
            # 这是一个简化的解析器，实际格式可能更复杂

            # 跳过可能的文件头
            if reader.length > 32:
                # 尝试读取一些基本信息
                try:
                    # 假设前几个字节包含版本或标识信息
                    header = reader.read_uint(4, True)
                    logger.debug(f"IMD文件头: 0x{header:08X}")

                    # 根据JavaScript代码，IMD可能包含tempo、tracks等信息
                    # 这里做简化处理

                except struct.error:
                    logger.warning("无法读取IMD文件头")
                    return []

            # 尝试解析音符数据
            # 这是一个简化的实现，实际格式需要更详细的逆向工程
            while not reader.eof() and len(notes) < 1000:  # 限制最大音符数
                try:
                    # 假设每个音符包含时间、轨道、类型等信息
                    time_tick = reader.read_uint(4, True)
                    track = reader.read_uint(1, True)
                    note_type = reader.read_uint(1, True)
                    duration_tick = reader.read_uint(2, True)

                    # 转换为秒（假设480 ticks = 1拍，120 BPM）
                    time_sec = (time_tick / 480.0) * (60.0 / 120.0)
                    duration_sec = (duration_tick / 480.0) * (60.0 / 120.0)

                    # 验证数据合理性
                    if (0 <= track <= 7 and 0 <= time_sec <= 300 and
                        0 <= duration_sec <= 10):

                        # 映射音符类型
                        type_map = {0: "tap", 1: "hold", 2: "slide", 3: "tap"}
                        note_type_str = type_map.get(note_type, "tap")

                        note = NoteInfo(
                            time=float(time_sec),
                            track=int(track),
                            note_type=note_type_str,
                            duration=max(0.1, float(duration_sec))
                        )
                        notes.append(note)

                        if len(notes) >= 500:  # 限制音符数量
                            break

                except struct.error:
                    # 到达文件末尾或数据错误
                    break
                except Exception as e:
                    logger.debug(f"解析音符时出错: {e}")
                    break

            logger.info(f"从IMD文件解析出 {len(notes)} 个音符")

        except Exception as e:
            logger.warning(f"解析IMD音符数据时出错: {e}")

        return notes

    def _parse_imd_notes(self, data: bytes) -> List[NoteInfo]:
        """
        解析IMD文件中的音符数据

        基于rmstZ工具的IMD格式分析：
        - 4字节：文件长度
        - 4字节：节拍数据长度
        - 节拍数据：每个12字节（4字节时间 + 8字节BPM）
        - 2字节：固定值（3,3）
        - 4字节：动作数据长度
        - 动作数据：每个11字节（2字节动作类型 + 4字节时间 + 1字节轨道 + 4字节参数）

        Args:
            data: 二进制数据

        Returns:
            List[NoteInfo]: 音符列表
        """
        notes = []

        try:
            if len(data) < 8:
                logger.warning("IMD文件太小，无法解析")
                return notes

            offset = 0

            # 读取文件长度
            file_length = struct.unpack('<I', data[offset:offset+4])[0]
            offset += 4

            # 读取节拍数据长度
            beat_data_count = struct.unpack('<I', data[offset:offset+4])[0]
            offset += 4

            logger.info(f"IMD文件长度: {file_length}, 节拍数据数量: {beat_data_count}")

            # 跳过节拍数据（每个12字节）
            beat_data_size = beat_data_count * 12
            if offset + beat_data_size > len(data):
                logger.warning("节拍数据超出文件范围")
                return notes
            offset += beat_data_size

            # 跳过固定值（2字节）
            if offset + 2 > len(data):
                logger.warning("文件在固定值处截断")
                return notes
            offset += 2

            # 读取动作数据长度
            if offset + 4 > len(data):
                logger.warning("无法读取动作数据长度")
                return notes
            action_data_count = struct.unpack('<I', data[offset:offset+4])[0]
            offset += 4

            logger.info(f"动作数据数量: {action_data_count}")

            # 解析动作数据（每个11字节）
            track_count = getattr(self, '_current_track_count', 4)

            for i in range(action_data_count):
                if offset + 11 > len(data):
                    logger.warning(f"动作数据在第{i}个条目处截断")
                    break

                # 2字节动作类型
                action_type = struct.unpack('<H', data[offset:offset+2])[0]
                offset += 2

                # 4字节时间（毫秒）
                time_ms = struct.unpack('<I', data[offset:offset+4])[0]
                offset += 4

                # 1字节轨道
                track = struct.unpack('<B', data[offset:offset+1])[0]
                offset += 1

                # 4字节参数
                param = struct.unpack('<I', data[offset:offset+4])[0]
                offset += 4

                # 转换时间为秒
                time_sec = time_ms / 1000.0

                # 确保轨道在有效范围内
                if track >= track_count:
                    track = track % track_count

                # 根据动作类型确定音符类型和属性（基于rmstZ的分析）
                note_type = "tap"
                duration = 0.1
                end_track = track  # 结束轨道（用于滑动音符）

                if action_type in [0x02, 0x62, 0x22, 0xA2]:  # 长按类型
                    note_type = "hold"
                    # param是持续时间（毫秒）
                    duration = max(0.1, param / 1000.0)
                    logger.debug(f"长按音符: 时间={time_sec:.3f}s, 轨道={track}, 持续={duration:.3f}s")

                elif action_type in [0x01, 0x61, 0x21, 0xA1]:  # 滑动类型
                    note_type = "slide"
                    # param是轨道偏移量
                    end_track = (track + param) % track_count
                    # 滑动音符的持续时间通常很短
                    duration = 0.2
                    logger.debug(f"滑动音符: 时间={time_sec:.3f}s, 起始轨道={track}, 结束轨道={end_track}")

                elif action_type == 0x00:  # 普通点击
                    note_type = "tap"
                    duration = 0.1
                    logger.debug(f"点击音符: 时间={time_sec:.3f}s, 轨道={track}")

                else:
                    # 其他类型，默认为点击
                    note_type = "tap"
                    duration = 0.1
                    logger.debug(f"未知动作类型 0x{action_type:02X}: 时间={time_sec:.3f}s, 轨道={track}")

                # 创建音符对象
                note = NoteInfo(
                    time=float(time_sec),
                    track=int(track),
                    note_type=note_type,
                    duration=float(duration),
                    end_track=int(end_track) if note_type == "slide" and end_track != track else None
                )

                notes.append(note)

        except Exception as e:
            logger.warning(f"解析IMD音符数据时出错: {e}")

        # 如果无法解析出音符，返回空列表
        if not notes:
            logger.warning("无法解析IMD音符数据，返回空列表")
        else:
            logger.info(f"成功解析 {len(notes)} 个音符")
            # 修复时间冲突
            notes = self._fix_timing_conflicts(notes)
            logger.info(f"时间冲突修复后: {len(notes)} 个音符")

        return notes

    def _fix_timing_conflicts(self, notes: List[NoteInfo]) -> List[NoteInfo]:
        """
        修复时间冲突问题

        确保同一轨道上的音符时间不重叠，特别是长按音符结束后的下一个音符

        Args:
            notes: 原始音符列表

        Returns:
            List[NoteInfo]: 修复后的音符列表
        """
        if not notes:
            return notes

        # 按时间排序
        notes.sort(key=lambda x: x.time)

        # 记录每个轨道的最后结束时间
        track_end_times = {}
        fixed_notes = []

        for note in notes:
            track = note.track
            current_time = note.time

            # 计算当前音符的结束时间
            end_time = current_time + note.duration

            # 检查是否与同轨道的前一个音符冲突
            if track in track_end_times:
                last_end_time = track_end_times[track]

                # 如果当前音符开始时间早于前一个音符结束时间，需要调整
                if current_time < last_end_time:
                    # 调整当前音符的开始时间为前一个音符结束时间
                    time_diff = last_end_time - current_time
                    adjusted_time = last_end_time + 0.001  # 添加1毫秒间隔避免完全重叠

                    logger.debug(f"修复时间冲突: 轨道{track}, 原时间{current_time:.3f}s → {adjusted_time:.3f}s (冲突{time_diff:.3f}s)")

                    # 创建修复后的音符
                    fixed_note = NoteInfo(
                        time=adjusted_time,
                        track=note.track,
                        note_type=note.note_type,
                        duration=note.duration,
                        velocity=note.velocity,
                        end_track=note.end_track
                    )

                    # 更新结束时间
                    track_end_times[track] = adjusted_time + note.duration
                    fixed_notes.append(fixed_note)
                else:
                    # 没有冲突，直接添加
                    track_end_times[track] = end_time
                    fixed_notes.append(note)
            else:
                # 第一个音符，直接添加
                track_end_times[track] = end_time
                fixed_notes.append(note)

        return fixed_notes

    def _calculate_track_from_context(self, note_index: int, time_sec: float, total_notes: int) -> int:
        """
        基于上下文计算音符轨道

        由于IMD二进制格式解析轨道信息不准确，使用启发式方法分配轨道

        Args:
            note_index: 音符索引
            time_sec: 音符时间（秒）
            total_notes: 当前总音符数

        Returns:
            int: 计算出的轨道编号
        """
        # 从文件名获取轨道数信息
        track_count = getattr(self, '_current_track_count', 4)

        # 使用多种策略来分配轨道，确保分布合理

        # 策略1: 基于时间的周期性分配
        time_based_track = int((time_sec * 2) % track_count)

        # 策略2: 基于音符索引的分配
        index_based_track = note_index % track_count

        # 策略3: 基于时间段的分配（每0.5秒切换轨道）
        time_segment = int(time_sec / 0.5)
        segment_based_track = time_segment % track_count

        # 组合策略：根据时间和索引选择最合适的轨道
        if note_index % 3 == 0:
            return time_based_track
        elif note_index % 3 == 1:
            return index_based_track
        else:
            return segment_based_track

    def _extract_info_from_filename(self, filename: str) -> tuple:
        """
        从文件名提取信息
        
        Args:
            filename: 文件名（不含扩展名）
            
        Returns:
            tuple: (标题, 轨道数, 难度)
        """
        # 解析文件名格式：歌名_4k_ez
        parts = filename.split('_')
        
        if len(parts) >= 3:
            title = parts[0]
            track_info = parts[1]
            difficulty = parts[2]
            
            # 提取轨道数
            if 'k' in track_info:
                try:
                    track_count = int(track_info.replace('k', ''))
                except ValueError:
                    track_count = 4
            else:
                track_count = 4
                
        else:
            title = filename
            track_count = 4
            difficulty = "nm"
        
        return title, track_count, difficulty
    
    def _map_difficulty_to_number(self, difficulty: str) -> int:
        """
        将难度字符串映射为数字
        
        Args:
            difficulty: 难度字符串
            
        Returns:
            int: 难度数字
        """
        difficulty_map = {
            'ez': 3,
            'nm': 6,
            'hd': 9
        }
        
        return difficulty_map.get(difficulty.lower(), 5)
    
    def _log_import_info(self, chart_data: ChartData, file_path: str):
        """记录导入信息"""
        logger.info(f"成功导入IMD文件: {file_path}")
        logger.info(f"  标题: {chart_data.metadata.title}")
        logger.info(f"  轨道数: {chart_data.metadata.track_count}")
        logger.info(f"  难度: {chart_data.metadata.difficulty}")
        logger.info(f"  音符数: {len(chart_data.get_all_notes())}")
        logger.info(f"  时长: {chart_data.metadata.duration:.2f}秒")
