#!/usr/bin/env python3
"""
训练数据预处理脚本

处理谱面文件，提取特征并准备训练数据
"""

import argparse
import logging
from pathlib import Path
import shutil
import json
from typing import List, Dict, Any
import librosa
import numpy as np

from src.format_converters import import_chart_from_file
from src.utils.logger_setup import setup_logging

logger = logging.getLogger(__name__)


def parse_args():
    """解析命令行参数"""
    parser = argparse.ArgumentParser(description='准备训练数据')
    
    parser.add_argument('--chart-dir', type=str, required=True,
                       help='谱面文件目录路径')
    parser.add_argument('--audio-dir', type=str, default=None,
                       help='音频文件目录路径（可选）')
    parser.add_argument('--output-dir', type=str, default='data/processed',
                       help='输出目录路径')
    parser.add_argument('--sample-rate', type=int, default=22050,
                       help='音频采样率')
    parser.add_argument('--max-duration', type=float, default=300.0,
                       help='最大音频时长（秒）')
    parser.add_argument('--log-level', type=str, default='INFO',
                       choices=['DEBUG', 'INFO', 'WARNING', 'ERROR'],
                       help='日志级别')
    parser.add_argument('--limit', type=int, default=None,
                       help='限制处理的谱面数量（用于测试）')

    return parser.parse_args()


def find_song_groups(chart_dir: Path, limit_songs: int = None) -> Dict[str, Dict]:
    """查找歌曲分组 - 每首歌包含音频文件和对应的谱面文件"""
    song_groups = {}

    logger.info(f"搜索歌曲文件夹...")

    # 遍历所有文件夹
    for song_folder in chart_dir.iterdir():
        if song_folder.is_dir():
            song_name = song_folder.name

            # 查找该歌曲的音频文件和谱面文件
            audio_file = None
            chart_files = []

            for file in song_folder.iterdir():
                if file.suffix.lower() == '.mp3':
                    audio_file = file
                elif file.suffix.lower() == '.imd':
                    chart_files.append(file)

            # 只有同时有音频和谱面文件的歌曲才加入
            if audio_file and chart_files:
                song_groups[song_name] = {
                    'audio': audio_file,
                    'charts': chart_files
                }
                logger.info(f"  找到歌曲: {song_name} (音频: 1, 谱面: {len(chart_files)})")

                # 如果达到限制数量，停止搜索
                if limit_songs and len(song_groups) >= limit_songs:
                    break

    total_charts = sum(len(group['charts']) for group in song_groups.values())
    logger.info(f"总共找到 {len(song_groups)} 首歌曲，{total_charts} 个谱面文件")
    return song_groups


def extract_song_name(chart_file: Path) -> str:
    """从谱面文件名提取歌曲名称"""
    base_name = chart_file.stem

    # 如果谱面文件是.mcz，去掉可能的后缀
    if base_name.endswith('.mc'):
        base_name = base_name[:-3]

    # 处理节奏大师格式的文件名
    # 例如: "节奏大师-阿多尼斯_4k_ez.imd" -> "节奏大师-阿多尼斯"
    if '_' in base_name:
        # 找到最后一个下划线之前的部分
        parts = base_name.split('_')
        if len(parts) >= 3:  # 格式: 歌名_键位_难度
            base_name = '_'.join(parts[:-2])  # 去掉键位和难度部分

    return base_name

def find_audio_file(song_name: str, search_dir: Path) -> Path:
    """查找对应的音频文件"""
    # 音频文件扩展名
    audio_extensions = ['.mp3', '.wav', '.flac', '.m4a', '.ogg']

    for ext in audio_extensions:
        audio_file = search_dir / f"{song_name}{ext}"
        if audio_file.exists():
            return audio_file

    return None


def process_chart_file(chart_file: Path, output_dir: Path, audio_dir: Path = None,
                      processed_audio: Dict[str, str] = None) -> Dict[str, Any]:
    """处理单个谱面文件"""
    logger.info(f"处理谱面文件: {chart_file.name}")

    try:
        # 导入谱面数据
        chart_data = import_chart_from_file(str(chart_file))
        if not chart_data:
            logger.error(f"无法导入谱面: {chart_file}")
            return None

        # 提取歌曲名称
        song_name = extract_song_name(chart_file)

        # 查找对应的音频文件
        search_dir = audio_dir if audio_dir else chart_file.parent
        audio_file = find_audio_file(song_name, search_dir)
        if not audio_file:
            logger.warning(f"未找到对应音频文件: {song_name}")
            return None

        # 创建输出文件名
        chart_base_name = chart_file.stem
        if chart_base_name.endswith('.mc'):
            chart_base_name = chart_base_name[:-3]

        # 处理音频文件（去重）
        audio_output_dir = output_dir / "audio"
        audio_output_dir.mkdir(parents=True, exist_ok=True)

        # 使用歌曲名作为音频文件名，避免重复
        audio_output_file = audio_output_dir / f"{song_name}.mp3"

        # 检查是否已经处理过这个音频文件
        if processed_audio is None:
            processed_audio = {}

        if song_name not in processed_audio:
            # 第一次处理这首歌的音频
            if not audio_output_file.exists():
                # 如果不是mp3格式，转换为mp3
                if audio_file.suffix.lower() != '.mp3':
                    convert_audio_to_mp3(audio_file, audio_output_file)
                else:
                    shutil.copy2(audio_file, audio_output_file)
            processed_audio[song_name] = str(audio_output_file)
            logger.info(f"处理音频文件: {song_name}")
        else:
            logger.debug(f"音频文件已存在，跳过: {song_name}")

        # 保存谱面数据为JSON格式
        charts_output_dir = output_dir / "charts"
        charts_output_dir.mkdir(parents=True, exist_ok=True)

        chart_output_file = charts_output_dir / f"{chart_base_name}.json"
        save_chart_as_json(chart_data, chart_output_file)

        # 返回处理结果
        return {
            'chart_file': str(chart_file),
            'audio_file': str(audio_file),
            'output_audio': processed_audio[song_name],  # 使用共享的音频文件
            'output_chart': str(chart_output_file),
            'song_name': song_name,  # 添加歌曲名称
            'title': chart_data.metadata.title,
            'artist': chart_data.metadata.artist,
            'difficulty': chart_data.metadata.difficulty,
            'bpm': chart_data.metadata.bpm,
            'duration': chart_data.metadata.duration,
            'track_count': chart_data.metadata.track_count,
            'note_count': len(chart_data.get_all_notes())
        }

    except Exception as e:
        logger.error(f"处理谱面文件失败 {chart_file}: {e}")
        return None


def convert_audio_to_mp3(input_file: Path, output_file: Path):
    """转换音频文件为MP3格式"""
    try:
        # 使用librosa加载音频
        y, sr = librosa.load(str(input_file), sr=22050)
        
        # 保存为wav格式（librosa不直接支持mp3输出）
        import soundfile as sf
        temp_wav = output_file.with_suffix('.wav')
        sf.write(str(temp_wav), y, sr)
        
        # 如果需要mp3格式，可以使用ffmpeg或其他工具转换
        # 这里暂时保存为wav格式
        if output_file.suffix.lower() == '.mp3':
            output_file = output_file.with_suffix('.wav')
        
        if temp_wav != output_file:
            shutil.move(str(temp_wav), str(output_file))
            
        logger.info(f"音频转换完成: {output_file}")
        
    except Exception as e:
        logger.error(f"音频转换失败 {input_file}: {e}")
        # 如果转换失败，直接复制原文件
        shutil.copy2(input_file, output_file)


def save_chart_as_json(chart_data, output_file: Path):
    """保存谱面数据为JSON格式"""
    # 转换为可序列化的格式
    chart_dict = {
        'metadata': {
            'title': chart_data.metadata.title,
            'artist': chart_data.metadata.artist,
            'creator': chart_data.metadata.creator,
            'difficulty': chart_data.metadata.difficulty,
            'bpm': chart_data.metadata.bpm,
            'duration': chart_data.metadata.duration,
            'track_count': chart_data.metadata.track_count
        },
        'notes': []
    }
    
    # 转换音符数据
    for note in chart_data.get_all_notes():
        note_dict = {
            'time': note.time,
            'track': note.track,
            'note_type': note.note_type,
            'duration': note.duration
        }
        chart_dict['notes'].append(note_dict)
    
    # 保存JSON文件
    with open(output_file, 'w', encoding='utf-8') as f:
        json.dump(chart_dict, f, indent=2, ensure_ascii=False)


def main():
    """主函数"""
    args = parse_args()

    # 设置环境变量以支持中文输出
    import os
    import sys
    os.environ['PYTHONIOENCODING'] = 'utf-8'
    if sys.platform.startswith('win'):
        os.system('chcp 65001 >nul 2>&1')

    # 设置日志
    setup_logging(level=args.log_level)

    print("AI训练数据预处理器")
    print("=" * 50)
    
    # 创建输出目录
    output_dir = Path(args.output_dir)
    output_dir.mkdir(parents=True, exist_ok=True)
    
    # 查找谱面文件
    chart_dir = Path(args.chart_dir)
    if not chart_dir.exists():
        logger.error(f"谱面目录不存在: {chart_dir}")
        return 1
    
    # 查找歌曲分组，限制歌曲数量而不是谱面数量
    song_groups = find_song_groups(chart_dir, limit_songs=args.limit)
    if not song_groups:
        logger.error(f"未找到歌曲文件: {chart_dir}")
        return 1

    print(f"将处理 {len(song_groups)} 首歌曲")
    
    # 音频目录
    audio_dir = Path(args.audio_dir) if args.audio_dir else None
    if audio_dir and not audio_dir.exists():
        logger.warning(f"音频目录不存在: {audio_dir}")
        audio_dir = None
    
    # 处理歌曲分组
    processed_data = []
    success_count = 0
    total_charts = 0
    processed_audio = {}  # 跟踪已处理的音频文件

    for song_name, song_data in song_groups.items():
        print(f"\n处理歌曲: {song_name}")
        audio_file = song_data['audio']
        chart_files = song_data['charts']

        for i, chart_file in enumerate(chart_files, 1):
            total_charts += 1
            print(f"  谱面 {i}/{len(chart_files)}: {chart_file.name}")

            result = process_chart_file(chart_file, output_dir, audio_dir, processed_audio)
            if result:
                processed_data.append(result)
                success_count += 1

        # 显示进度
        print(f"  歌曲 {song_name} 完成，成功处理 {len([r for r in processed_data if song_name in str(r)])} 个谱面")

    # 保存处理结果
    metadata_file = output_dir / "dataset_metadata.json"
    with open(metadata_file, 'w', encoding='utf-8') as f:
        json.dump({
            'total_songs': len(song_groups),
            'total_charts': total_charts,
            'processed_files': len(processed_data),
            'success_rate': len(processed_data) / total_charts if total_charts else 0,
            'data': processed_data
        }, f, indent=2, ensure_ascii=False)

    print(f"\n数据预处理完成！")
    print(f"   处理歌曲: {len(song_groups)} 首")
    print(f"   总谱面数: {total_charts}")
    print(f"   成功处理: {len(processed_data)}")
    print(f"   成功率: {len(processed_data)/total_charts*100:.1f}%")
    print(f"   输出目录: {output_dir}")
    print(f"   元数据文件: {metadata_file}")
    
    return 0


if __name__ == "__main__":
    exit(main())
