#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
数据增强模块
用于改善训练数据质量，解决轨道分布不均等问题
"""

import torch
import numpy as np
import random
from typing import Dict, Tuple, List
import copy

class ChartDataAugmentation:
    """谱面数据增强器"""
    
    def __init__(
        self,
        track_shuffle_prob: float = 0.3,
        note_dropout_prob: float = 0.05,
        track_balance_prob: float = 0.4,
        time_stretch_prob: float = 0.2
    ):
        """
        初始化数据增强器
        
        Args:
            track_shuffle_prob: 轨道随机打乱概率
            note_dropout_prob: 音符随机丢弃概率
            track_balance_prob: 轨道平衡增强概率
            time_stretch_prob: 时间拉伸概率
        """
        self.track_shuffle_prob = track_shuffle_prob
        self.note_dropout_prob = note_dropout_prob
        self.track_balance_prob = track_balance_prob
        self.time_stretch_prob = time_stretch_prob
    
    def augment_sample(self, sample: Dict) -> Dict:
        """
        对单个样本进行数据增强
        
        Args:
            sample: 包含audio_features, chart, quality, difficulty的字典
            
        Returns:
            Dict: 增强后的样本
        """
        augmented_sample = copy.deepcopy(sample)
        
        # 轨道随机打乱
        if random.random() < self.track_shuffle_prob:
            augmented_sample = self._shuffle_tracks(augmented_sample)
        
        # 音符随机丢弃
        if random.random() < self.note_dropout_prob:
            augmented_sample = self._dropout_notes(augmented_sample)
        
        # 轨道平衡增强
        if random.random() < self.track_balance_prob:
            augmented_sample = self._balance_tracks(augmented_sample)
        
        # 时间拉伸
        if random.random() < self.time_stretch_prob:
            augmented_sample = self._time_stretch(augmented_sample)
        
        return augmented_sample
    
    def _shuffle_tracks(self, sample: Dict) -> Dict:
        """随机打乱轨道顺序"""
        chart = sample['chart']  # [time, tracks]
        track_count = chart.shape[1]
        
        # 生成随机轨道映射
        track_mapping = list(range(track_count))
        random.shuffle(track_mapping)
        
        # 重新排列轨道
        new_chart = torch.zeros_like(chart)
        for old_track, new_track in enumerate(track_mapping):
            new_chart[:, new_track] = chart[:, old_track]
        
        sample['chart'] = new_chart
        return sample
    
    def _dropout_notes(self, sample: Dict) -> Dict:
        """随机丢弃一些音符"""
        chart = sample['chart']  # [time, tracks]
        
        # 创建随机掩码
        dropout_mask = torch.rand_like(chart.float()) > self.note_dropout_prob
        
        # 只对非空音符应用dropout
        note_mask = (chart > 0)
        final_mask = dropout_mask | (~note_mask)
        
        sample['chart'] = chart * final_mask.long()
        return sample
    
    def _balance_tracks(self, sample: Dict) -> Dict:
        """平衡轨道分布"""
        chart = sample['chart']  # [time, tracks]
        track_count = chart.shape[1]
        
        # 计算每个轨道的音符数量
        track_note_counts = []
        for track in range(track_count):
            count = torch.sum(chart[:, track] > 0).item()
            track_note_counts.append(count)
        
        # 找到音符最多和最少的轨道
        max_count = max(track_note_counts)
        min_count = min(track_note_counts)
        
        # 如果分布过于不均匀，进行调整
        if max_count > min_count * 3:  # 最多轨道是最少轨道的3倍以上
            max_track = track_note_counts.index(max_count)
            min_track = track_note_counts.index(min_count)
            
            # 将一些音符从最多的轨道移动到最少的轨道
            max_track_notes = torch.nonzero(chart[:, max_track] > 0).squeeze()
            if len(max_track_notes.shape) > 0 and len(max_track_notes) > 0:
                # 随机选择一些音符进行移动
                move_count = min(len(max_track_notes) // 4, max_count - min_count)
                if move_count > 0:
                    move_indices = random.sample(range(len(max_track_notes)), move_count)
                    
                    for idx in move_indices:
                        time_step = max_track_notes[idx].item()
                        if chart[time_step, min_track] == 0:  # 目标位置为空
                            # 移动音符
                            chart[time_step, min_track] = chart[time_step, max_track]
                            chart[time_step, max_track] = 0
        
        sample['chart'] = chart
        return sample
    
    def _time_stretch(self, sample: Dict) -> Dict:
        """时间拉伸（简单的插值或抽样）"""
        chart = sample['chart']  # [time, tracks]
        audio_features = sample['audio_features']  # [features, time]
        
        # 随机选择拉伸因子
        stretch_factor = random.uniform(0.8, 1.2)
        
        if stretch_factor == 1.0:
            return sample
        
        # 计算新的长度
        new_time_length = int(chart.shape[0] * stretch_factor)
        new_audio_length = int(audio_features.shape[1] * stretch_factor)
        
        # 对谱面进行插值
        if stretch_factor > 1.0:  # 拉长
            # 使用最近邻插值
            indices = torch.linspace(0, chart.shape[0] - 1, new_time_length).long()
            new_chart = chart[indices]
        else:  # 压缩
            # 使用步长采样
            step = int(1 / stretch_factor)
            new_chart = chart[::step][:new_time_length]
        
        # 对音频特征进行插值
        if stretch_factor > 1.0:
            indices = torch.linspace(0, audio_features.shape[1] - 1, new_audio_length).long()
            new_audio_features = audio_features[:, indices]
        else:
            step = int(1 / stretch_factor)
            new_audio_features = audio_features[:, ::step][:, :new_audio_length]
        
        sample['chart'] = new_chart
        sample['audio_features'] = new_audio_features
        return sample
    
    def analyze_track_distribution(self, chart: torch.Tensor) -> Dict:
        """分析轨道分布"""
        track_count = chart.shape[1]
        track_stats = {}
        
        for track in range(track_count):
            track_notes = torch.sum(chart[:, track] > 0).item()
            track_stats[f'track_{track}'] = track_notes
        
        # 计算分布均匀性
        counts = list(track_stats.values())
        if len(counts) > 0:
            mean_count = np.mean(counts)
            std_count = np.std(counts)
            balance_score = 1 - (std_count / (mean_count + 1e-8))  # 越接近1越均匀
        else:
            balance_score = 0
        
        track_stats['balance_score'] = balance_score
        track_stats['total_notes'] = sum(counts)
        
        return track_stats

class AudioDataAugmentation:
    """音频数据增强器"""
    
    def __init__(
        self,
        noise_prob: float = 0.1,
        pitch_shift_prob: float = 0.2,
        time_mask_prob: float = 0.1
    ):
        self.noise_prob = noise_prob
        self.pitch_shift_prob = pitch_shift_prob
        self.time_mask_prob = time_mask_prob
    
    def augment_audio_features(self, audio_features: torch.Tensor) -> torch.Tensor:
        """
        对音频特征进行增强
        
        Args:
            audio_features: [features, time] 音频特征
            
        Returns:
            torch.Tensor: 增强后的音频特征
        """
        augmented_features = audio_features.clone()
        
        # 添加噪声
        if random.random() < self.noise_prob:
            noise = torch.randn_like(augmented_features) * 0.01
            augmented_features += noise
        
        # 时间掩码
        if random.random() < self.time_mask_prob:
            augmented_features = self._apply_time_mask(augmented_features)
        
        # 特征掩码
        if random.random() < 0.1:
            augmented_features = self._apply_feature_mask(augmented_features)
        
        return augmented_features
    
    def _apply_time_mask(self, features: torch.Tensor) -> torch.Tensor:
        """应用时间掩码"""
        time_length = features.shape[1]
        mask_length = random.randint(1, min(10, time_length // 10))
        mask_start = random.randint(0, time_length - mask_length)
        
        features[:, mask_start:mask_start + mask_length] = 0
        return features
    
    def _apply_feature_mask(self, features: torch.Tensor) -> torch.Tensor:
        """应用特征掩码"""
        feature_count = features.shape[0]
        mask_count = random.randint(1, min(5, feature_count // 10))
        mask_indices = random.sample(range(feature_count), mask_count)
        
        for idx in mask_indices:
            features[idx, :] = 0
        
        return features

def create_balanced_dataset(original_samples: List[Dict], target_balance: float = 0.8) -> List[Dict]:
    """
    创建轨道分布更均衡的数据集
    
    Args:
        original_samples: 原始样本列表
        target_balance: 目标平衡分数
        
    Returns:
        List[Dict]: 平衡后的样本列表
    """
    augmenter = ChartDataAugmentation(track_balance_prob=1.0)  # 总是应用轨道平衡
    balanced_samples = []
    
    for sample in original_samples:
        # 分析原始分布
        original_stats = augmenter.analyze_track_distribution(sample['chart'])
        
        if original_stats['balance_score'] < target_balance:
            # 需要平衡，应用多次增强
            for _ in range(3):  # 生成3个平衡版本
                balanced_sample = augmenter._balance_tracks(copy.deepcopy(sample))
                balanced_samples.append(balanced_sample)
        else:
            # 已经平衡，直接添加
            balanced_samples.append(sample)
    
    return balanced_samples
