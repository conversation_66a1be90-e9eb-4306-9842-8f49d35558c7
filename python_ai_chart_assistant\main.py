#!/usr/bin/env python3
"""
AI音游写谱助手主程序

提供统一的入口点来使用各种功能
"""

import sys
import argparse
from pathlib import Path
import logging

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

# 设置环境变量以支持中文输出
import os
os.environ['PYTHONIOENCODING'] = 'utf-8'
if sys.platform.startswith('win'):
    os.system('chcp 65001 >nul 2>&1')

from src.chart_generator import AIChartGenerator
from src.utils.logger_setup import setup_colored_logging
from src.utils.config_manager import ConfigManager

# 设置日志
setup_colored_logging(level="INFO")
logger = logging.getLogger(__name__)


def cmd_generate(args):
    """生成谱面命令"""
    try:
        generator = AIChartGenerator(
            model_path=args.model,
            device=args.device
        )
        
        print(f"🎵 正在生成谱面...")
        print(f"  输入: {args.input}")
        print(f"  输出: {args.output}")
        print(f"  格式: {args.format}")
        print(f"  难度: {args.difficulty}")
        print(f"  风格: {args.style}")
        
        success = generator.generate_from_midi(
            midi_path=args.input,
            output_path=args.output,
            format_name=args.format,
            difficulty=args.difficulty,
            style=args.style,
            title=args.title,
            artist=args.artist
        )
        
        if success:
            print("✅ 谱面生成成功！")
        else:
            print("❌ 谱面生成失败")
            sys.exit(1)
            
    except Exception as e:
        print(f"❌ 错误: {e}")
        sys.exit(1)


def cmd_analyze(args):
    """分析MIDI命令"""
    try:
        generator = AIChartGenerator()
        
        print(f"🔍 正在分析MIDI文件: {args.input}")
        
        analysis = generator.analyze_midi(args.input)
        
        if not analysis:
            print("❌ 分析失败")
            sys.exit(1)
        
        # 显示分析结果
        basic_info = analysis.get('basic_info', {})
        print(f"\n📊 基本信息:")
        print(f"  时长: {basic_info.get('duration', 0):.1f} 秒")
        print(f"  BPM: {basic_info.get('initial_bpm', 0):.1f}")
        print(f"  总音符数: {basic_info.get('total_notes', 0)}")
        print(f"  轨道数: {basic_info.get('total_instruments', 0)}")
        
        # 主旋律信息
        main_melody = analysis.get('main_melody')
        if main_melody:
            print(f"\n🎵 主旋律轨道:")
            print(f"  轨道名称: {main_melody.get('name', 'Unknown')}")
            print(f"  音符数量: {main_melody.get('note_count', 0)}")
        
        # 节奏信息
        rhythm = analysis.get('rhythm_patterns', {})
        if 'note_density' in rhythm:
            print(f"\n🥁 节奏信息:")
            print(f"  音符密度: {rhythm['note_density']:.2f} 音符/秒")
            print(f"  平均间隔: {rhythm.get('avg_interval', 0):.3f} 秒")
        
        # 和弦信息
        chords = analysis.get('chord_progressions', [])
        if chords:
            print(f"\n🎹 和弦信息:")
            print(f"  检测到 {len(chords)} 个和弦")
            for i, chord in enumerate(chords[:3]):  # 显示前3个
                time = chord.get('time', 0)
                chord_type = chord.get('chord_type', 'unknown')
                print(f"  {time:.1f}s: {chord_type}")
            if len(chords) > 3:
                print(f"  ... 还有 {len(chords) - 3} 个和弦")
        
    except Exception as e:
        print(f"❌ 错误: {e}")
        sys.exit(1)


def cmd_predict(args):
    """预测难度命令"""
    try:
        generator = AIChartGenerator()
        
        # 加载难度预测器
        if args.predictor:
            generator.load_difficulty_predictor(args.predictor)
        
        print(f"🎯 正在预测难度: {args.input}")
        
        result = generator.predict_difficulty(args.input)
        
        print(f"预测难度: {result.get('difficulty', 'N/A'):.1f}")
        print(f"置信度: {result.get('confidence', 0):.2f}")
        
        if 'playability' in result:
            print(f"游戏性评分: {result['playability']:.2f}")
        
        if 'note_distribution' in result:
            dist = result['note_distribution']
            print(f"音符分布: 空白={dist[0]:.1%}, 短音符={dist[1]:.1%}, 长音符={dist[2]:.1%}")
        
    except Exception as e:
        print(f"❌ 错误: {e}")
        sys.exit(1)


def cmd_convert(args):
    """格式转换命令"""
    try:
        generator = AIChartGenerator()
        
        print(f"🔄 正在转换格式:")
        print(f"  输入: {args.input}")
        print(f"  输出: {args.output}")
        print(f"  目标格式: {args.format}")
        
        success = generator.convert_chart(
            input_path=args.input,
            output_path=args.output,
            target_format=args.format
        )
        
        if success:
            print("✅ 格式转换成功！")
        else:
            print("❌ 格式转换失败")
            sys.exit(1)
            
    except Exception as e:
        print(f"❌ 错误: {e}")
        sys.exit(1)


def cmd_train(args):
    """训练模型命令"""
    try:
        from src.training.train import ModelTrainer
        
        print(f"🚀 开始训练模型...")
        print(f"  数据目录: {args.data_dir}")
        print(f"  模型类型: {args.model_type}")
        print(f"  训练轮数: {args.epochs}")
        print(f"  设备: {args.device}")
        
        trainer = ModelTrainer(args.config, args.device)
        trainer.setup_model(args.model_type)
        trainer.setup_data_loader(args.data_dir)
        trainer.setup_optimizer()
        
        # 设置TensorBoard
        log_dir = Path(args.save_dir) / 'tensorboard_logs'
        trainer.setup_tensorboard(str(log_dir))
        
        # 恢复训练
        if args.resume:
            trainer.load_checkpoint(args.resume)
        
        # 开始训练
        trainer.train_generation_model(args.epochs, args.save_dir)
        
        print("✅ 训练完成！")
        
    except Exception as e:
        print(f"❌ 训练失败: {e}")
        sys.exit(1)


def cmd_api(args):
    """启动API服务命令"""
    try:
        from api.app import create_app
        
        print(f"🌐 启动API服务...")
        print(f"  地址: {args.host}:{args.port}")
        print(f"  调试模式: {args.debug}")
        
        app = create_app(args.config)
        app.run(
            host=args.host,
            port=args.port,
            debug=args.debug
        )
        
    except Exception as e:
        print(f"❌ API服务启动失败: {e}")
        sys.exit(1)


def cmd_demo(args):
    """运行演示命令"""
    try:
        from examples.basic_usage import main as demo_main
        
        print("🎮 运行基础演示...")
        demo_main()
        
    except Exception as e:
        print(f"❌ 演示运行失败: {e}")
        sys.exit(1)


def main():
    """主函数"""
    parser = argparse.ArgumentParser(
        description="🎵 AI音游写谱助手",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
示例用法:
  %(prog)s generate input.mid output.mc --difficulty 7 --style balanced
  %(prog)s analyze input.mid
  %(prog)s predict input.mid --predictor models/difficulty_predictor.pth
  %(prog)s convert input.json output.mc --format malody
  %(prog)s train --data-dir data --epochs 100 --device cuda
  %(prog)s api --host 0.0.0.0 --port 5000
  %(prog)s demo
        """
    )
    
    # 全局参数
    parser.add_argument('--config', '-c', help='配置文件路径')
    parser.add_argument('--verbose', '-v', action='store_true', help='详细输出')
    
    # 子命令
    subparsers = parser.add_subparsers(dest='command', help='可用命令')
    
    # 生成谱面命令
    gen_parser = subparsers.add_parser('generate', help='从MIDI文件生成谱面')
    gen_parser.add_argument('input', help='输入MIDI文件')
    gen_parser.add_argument('output', help='输出谱面文件')
    gen_parser.add_argument('--format', '-f', default='malody',
                           choices=['malody', 'rhythm_master', 'osu'],
                           help='输出格式')
    gen_parser.add_argument('--difficulty', '-d', type=int, default=5,
                           help='难度等级 (1-10)')
    gen_parser.add_argument('--style', '-s', default='balanced',
                           choices=['balanced', 'dense', 'sparse', 'rhythmic'],
                           help='生成风格')
    gen_parser.add_argument('--title', help='歌曲标题')
    gen_parser.add_argument('--artist', help='艺术家')
    gen_parser.add_argument('--model', help='模型文件路径')
    gen_parser.add_argument('--device', default='cpu', help='计算设备')
    gen_parser.set_defaults(func=cmd_generate)
    
    # 分析MIDI命令
    analyze_parser = subparsers.add_parser('analyze', help='分析MIDI文件')
    analyze_parser.add_argument('input', help='输入MIDI文件')
    analyze_parser.set_defaults(func=cmd_analyze)
    
    # 预测难度命令
    predict_parser = subparsers.add_parser('predict', help='预测MIDI文件难度')
    predict_parser.add_argument('input', help='输入MIDI文件')
    predict_parser.add_argument('--predictor', help='难度预测模型路径')
    predict_parser.set_defaults(func=cmd_predict)
    
    # 格式转换命令
    convert_parser = subparsers.add_parser('convert', help='转换谱面格式')
    convert_parser.add_argument('input', help='输入谱面文件')
    convert_parser.add_argument('output', help='输出谱面文件')
    convert_parser.add_argument('--format', '-f', required=True,
                               choices=['malody', 'rhythm_master', 'osu'],
                               help='目标格式')
    convert_parser.set_defaults(func=cmd_convert)
    
    # 训练模型命令
    train_parser = subparsers.add_parser('train', help='训练AI模型')
    train_parser.add_argument('--data-dir', required=True, help='训练数据目录')
    train_parser.add_argument('--model-type', default='generation',
                             choices=['generation', 'difficulty'],
                             help='模型类型')
    train_parser.add_argument('--epochs', type=int, default=100, help='训练轮数')
    train_parser.add_argument('--save-dir', default='models', help='模型保存目录')
    train_parser.add_argument('--device', default='cpu', help='计算设备')
    train_parser.add_argument('--resume', help='恢复训练的检查点路径')
    train_parser.set_defaults(func=cmd_train)
    
    # API服务命令
    api_parser = subparsers.add_parser('api', help='启动API服务')
    api_parser.add_argument('--host', default='127.0.0.1', help='服务器地址')
    api_parser.add_argument('--port', type=int, default=5000, help='服务器端口')
    api_parser.add_argument('--debug', action='store_true', help='调试模式')
    api_parser.set_defaults(func=cmd_api)
    
    # 演示命令
    demo_parser = subparsers.add_parser('demo', help='运行基础演示')
    demo_parser.set_defaults(func=cmd_demo)
    
    # 解析参数
    args = parser.parse_args()
    
    # 设置日志级别
    if args.verbose:
        logging.getLogger().setLevel(logging.DEBUG)
    
    # 如果没有指定命令，显示帮助
    if not args.command:
        parser.print_help()
        return
    
    # 执行命令
    try:
        args.func(args)
    except KeyboardInterrupt:
        print("\n⏹️  用户中断")
    except Exception as e:
        logger.error(f"执行失败: {e}")
        if args.verbose:
            import traceback
            traceback.print_exc()
        sys.exit(1)


if __name__ == '__main__':
    main()
