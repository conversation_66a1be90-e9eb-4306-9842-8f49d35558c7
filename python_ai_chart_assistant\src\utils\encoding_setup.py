"""
编码设置工具

解决Windows系统中文编码问题
"""

import sys
import io
import os


def setup_console_encoding():
    """
    设置控制台编码为UTF-8以支持中文输出

    在Windows系统中，默认的控制台编码可能是cp1252或其他编码，
    这会导致中文字符无法正确显示。此函数将控制台编码设置为UTF-8。
    """
    if sys.platform.startswith('win'):
        try:
            # 设置控制台代码页为UTF-8
            os.system('chcp 65001 >nul 2>&1')

            # 安全地重新包装stdout为UTF-8编码
            if hasattr(sys.stdout, 'buffer') and not sys.stdout.closed and not is_already_wrapped(sys.stdout):
                try:
                    original_buffer = sys.stdout.buffer
                    sys.stdout = io.TextIOWrapper(original_buffer, encoding='utf-8', errors='replace', line_buffering=True)
                except (ValueError, OSError, AttributeError):
                    pass  # 如果失败，保持原状

            # 安全地重新包装stderr为UTF-8编码
            if hasattr(sys.stderr, 'buffer') and not sys.stderr.closed and not is_already_wrapped(sys.stderr):
                try:
                    original_buffer = sys.stderr.buffer
                    sys.stderr = io.TextIOWrapper(original_buffer, encoding='utf-8', errors='replace', line_buffering=True)
                except (ValueError, OSError, AttributeError):
                    pass  # 如果失败，保持原状

        except Exception as e:
            # 如果设置失败，至少尝试设置环境变量
            os.environ['PYTHONIOENCODING'] = 'utf-8'
            try:
                print(f"警告: 编码设置可能不完整: {e}")
            except:
                pass  # 如果连print都失败，就静默处理


def ensure_utf8_encoding():
    """
    确保Python使用UTF-8编码
    
    设置相关的环境变量以确保Python使用UTF-8编码
    """
    # 设置Python IO编码
    os.environ['PYTHONIOENCODING'] = 'utf-8'
    
    # 设置默认编码
    if hasattr(sys, 'setdefaultencoding'):
        sys.setdefaultencoding('utf-8')


def safe_print(*args, **kwargs):
    """
    安全的打印函数，自动处理编码问题
    
    Args:
        *args: 要打印的参数
        **kwargs: print函数的关键字参数
    """
    try:
        print(*args, **kwargs)
    except UnicodeEncodeError:
        # 如果出现编码错误，尝试使用ASCII编码并忽略错误
        encoded_args = []
        for arg in args:
            if isinstance(arg, str):
                encoded_args.append(arg.encode('ascii', 'ignore').decode('ascii'))
            else:
                encoded_args.append(str(arg))
        print(*encoded_args, **kwargs)


def is_already_wrapped(stream):
    """
    检查流是否已经被TextIOWrapper包装过

    Args:
        stream: 要检查的流对象

    Returns:
        bool: 如果已经被包装返回True
    """
    return isinstance(stream, io.TextIOWrapper) and hasattr(stream, 'buffer')


def init_encoding():
    """
    初始化编码设置

    这是一个便捷函数，会调用所有必要的编码设置函数
    """
    ensure_utf8_encoding()
    setup_console_encoding()


# 避免重复初始化
_encoding_initialized = False

def safe_init_encoding():
    """
    安全的编码初始化，避免重复初始化
    """
    global _encoding_initialized
    if not _encoding_initialized:
        init_encoding()
        _encoding_initialized = True
