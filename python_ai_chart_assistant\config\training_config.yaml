# AI音游谱面生成模型训练配置

# 数据配置
data:
  # 谱面文件目录
  chart_dir: "谱面文件"
  # 音频文件目录（可选）
  audio_dir: null
  # 处理后数据输出目录
  output_dir: "data/processed"
  # 音频采样率
  sample_rate: 22050
  # 最大音频时长（秒）
  max_duration: 300.0
  # 轨道数量
  track_count: 4
  # 最大序列长度
  max_length: 2000

# 模型配置
model:
  # 生成器配置
  generator:
    # 音频特征维度
    audio_feature_dim: 95
    # 隐藏层维度
    hidden_dim: 512
    # LSTM层数
    num_layers: 3
    # Dropout率
    dropout: 0.2
    
  # 判别器配置
  discriminator:
    # 隐藏层维度
    hidden_dim: 256
    # 卷积层数
    num_conv_layers: 4
    # Dropout率
    dropout: 0.3

# 训练配置
training:
  # 训练轮数
  epochs: 50
  # 批次大小
  batch_size: 8
  # 生成器学习率
  lr_generator: 0.0002
  # 判别器学习率
  lr_discriminator: 0.0002
  # 学习率衰减
  lr_decay: 0.95
  # 学习率衰减间隔
  lr_decay_step: 10
  # 验证集比例
  validation_split: 0.2
  # 早停耐心值
  early_stopping_patience: 10
  # 梯度裁剪
  gradient_clip: 1.0

  # 增量训练配置
  incremental:
    # 增量训练学习率（通常比初始训练更低）
    lr_generator: 0.0001
    lr_discriminator: 0.0001
    # 增量训练轮数
    epochs: 20
    # 新旧数据混合比例
    new_data_weight: 0.7
    old_data_weight: 0.3
    # 是否冻结部分层
    freeze_early_layers: false

# 损失函数权重
loss_weights:
  # 对抗损失权重
  adversarial: 1.0
  # 重构损失权重
  reconstruction: 10.0
  # 质量损失权重
  quality: 5.0
  # 难度损失权重
  difficulty: 2.0
  # 节拍对齐损失权重
  beat_alignment: 3.0

# 数据增强配置
augmentation:
  # 是否启用数据增强
  enabled: true
  # 时间拉伸概率
  time_stretch_prob: 0.3
  # 时间拉伸范围
  time_stretch_range: [0.9, 1.1]
  # 音频噪声概率
  audio_noise_prob: 0.2
  # 音频噪声强度
  audio_noise_strength: 0.01
  # 音符随机删除概率
  note_dropout_prob: 0.1
  # 音符随机删除比例
  note_dropout_rate: 0.05

# 设备配置
device:
  # 计算设备 (auto/cpu/cuda)
  type: "auto"
  # 数据加载器工作线程数
  num_workers: 4
  # 是否使用混合精度训练
  mixed_precision: true

# 保存配置
saving:
  # 模型保存目录
  model_dir: "models"
  # 保存间隔（轮数）
  save_interval: 5
  # 是否保存最佳模型
  save_best: true
  # 最佳模型评估指标
  best_metric: "quality_score"
  # 日志保存目录
  log_dir: "logs"

# 评估配置
evaluation:
  # 评估间隔（轮数）
  eval_interval: 1
  # 生成样本数量
  num_samples: 5
  # 评估指标
  metrics:
    - "quality_score"
    - "difficulty_accuracy"
    - "beat_alignment"
    - "note_density"
    - "track_balance"

# 可视化配置
visualization:
  # 是否启用TensorBoard
  tensorboard: true
  # TensorBoard日志目录
  tensorboard_dir: "runs"
  # 可视化间隔（轮数）
  vis_interval: 5
  # 生成样本可视化
  sample_visualization: true

# 调试配置
debug:
  # 是否启用调试模式
  enabled: false
  # 调试时使用的小数据集大小
  small_dataset_size: 100
  # 是否保存中间结果
  save_intermediate: false
  # 详细日志
  verbose_logging: false
