#!/usr/bin/env python3
"""
完整功能GAN训练脚本
包含多任务学习、复杂损失函数、WGAN-GP等高级功能
"""

import argparse
import sys
import logging
from pathlib import Path
import torch
import torch.nn as nn
import torch.optim as optim
from torch.utils.data import DataLoader, random_split
from torch.amp import autocast, GradScaler
from tqdm import tqdm

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from src.training.audio_chart_dataset import AudioChartDataset
from src.models.audio_chart_gan import AudioChartGAN

# 设置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def compute_gradient_penalty(discriminator, real_data, fake_data, device):
    """安全的梯度惩罚计算 - 避免之前的梯度冲突"""
    batch_size = real_data.size(0)
    
    # 随机插值系数
    alpha = torch.rand(batch_size, 1, 1, device=device)
    alpha = alpha.expand_as(real_data)
    
    # 创建插值数据 - 确保完全独立
    real_data_detached = real_data.detach().clone()
    fake_data_detached = fake_data.detach().clone()
    
    interpolated = alpha * real_data_detached + (1 - alpha) * fake_data_detached
    interpolated = interpolated.clone().detach().requires_grad_(True)
    
    # 计算判别器输出
    disc_interpolated = discriminator(interpolated)['real_fake']
    
    # 计算梯度
    gradients = torch.autograd.grad(
        outputs=disc_interpolated,
        inputs=interpolated,
        grad_outputs=torch.ones_like(disc_interpolated),
        create_graph=True,
        retain_graph=True,
        only_inputs=True
    )[0]
    
    # 梯度惩罚
    gradients = gradients.view(batch_size, -1)
    gradient_penalty = ((gradients.norm(2, dim=1) - 1) ** 2).mean()
    
    return gradient_penalty

def mixed_precision_train_step(model, real_charts, audio_features, target_quality, target_difficulty, 
                              optimizer_d, optimizer_g, scaler_d, scaler_g, device, 
                              use_wgan=True, use_amp=True, loss_weights=None):
    """混合精度训练步骤 - 彻底修复版本，按照PyTorch官方最佳实践"""
    batch_size = real_charts.size(0)
    
    # 重要：每次训练步骤开始时重置优化器
    optimizer_d.zero_grad()
    optimizer_g.zero_grad()
    
    # 默认损失权重
    if loss_weights is None:
        loss_weights = {
            'adversarial': 1.0,
            'quality': 0.5,
            'difficulty': 0.3,
            'music_matching': 0.8,
            'consistency': 0.2,
            'diversity': 0.1,
            'gradient_penalty': 10.0  # WGAN-GP梯度惩罚权重
        }
    
    # ===== 训练判别器 =====
    
    # 使用混合精度
    with autocast(device_type='cuda', enabled=use_amp):
        # 真实数据
        real_output = model.discriminator(real_charts)
        
        # 质量损失 - 使用Huber损失更稳定
        quality_target = target_quality.float().unsqueeze(1) if target_quality.dim() == 1 else target_quality.float()
        quality_loss = nn.SmoothL1Loss()(real_output['quality'], quality_target)
        
        # 难度损失 - 使用标签平滑
        difficulty_target = target_difficulty.long()
        difficulty_loss = nn.CrossEntropyLoss(label_smoothing=0.1)(real_output['difficulty'], difficulty_target)
        
        # 假数据 - 使用detach()断开梯度
        with torch.no_grad():
            fake_charts_probs = model.generator(audio_features)
            fake_charts = model._constrained_sampling(fake_charts_probs)
        
        fake_output = model.discriminator(fake_charts)
        
        if use_wgan:
            # WGAN损失 - Wasserstein距离
            d_real_score = torch.mean(real_output['real_fake'])
            d_fake_score = torch.mean(fake_output['real_fake'])
            d_adversarial_loss = d_fake_score - d_real_score
            
            # 梯度惩罚 - 在混合精度外计算
            gradient_penalty = compute_gradient_penalty(model.discriminator, real_charts, fake_charts, device)
            
        else:
            # 标准GAN损失
            real_labels = torch.ones(batch_size, 1, device=device)
            fake_labels = torch.zeros(batch_size, 1, device=device)
            d_real_loss = nn.BCELoss()(real_output['real_fake'], real_labels)
            d_fake_loss = nn.BCELoss()(fake_output['real_fake'], fake_labels)
            d_adversarial_loss = (d_real_loss + d_fake_loss) / 2
            gradient_penalty = torch.tensor(0.0, device=device)
        
        # 总判别器损失
        d_loss = (loss_weights['adversarial'] * d_adversarial_loss + 
                  loss_weights['quality'] * quality_loss + 
                  loss_weights['difficulty'] * difficulty_loss +
                  loss_weights['gradient_penalty'] * gradient_penalty)
    
    # 判别器反向传播 - 彻底修复：按照PyTorch官方文档
    if use_amp and scaler_d is not None:
        scaler_d.scale(d_loss).backward()
        # 关键修复：检查梯度是否有效
        scaler_d.unscale_(optimizer_d)
        # 可选：梯度裁剪
        torch.nn.utils.clip_grad_norm_(model.discriminator.parameters(), max_norm=1.0)
        scaler_d.step(optimizer_d)
        scaler_d.update()
    else:
        d_loss.backward()
        torch.nn.utils.clip_grad_norm_(model.discriminator.parameters(), max_norm=1.0)
        optimizer_d.step()
    
    # ===== 训练生成器 =====
    
    # 使用混合精度
    with autocast(device_type='cuda', enabled=use_amp):
        # 重新生成假数据用于生成器训练
        fake_charts_probs = model.generator(audio_features)
        fake_charts = model._constrained_sampling(fake_charts_probs)
        fake_output = model.discriminator(fake_charts)
        
        # 对抗损失
        if use_wgan:
            # WGAN生成器损失
            g_adversarial_loss = -torch.mean(fake_output['real_fake'])
        else:
            # 标准GAN生成器损失
            real_labels = torch.ones(batch_size, 1, device=device)
            g_adversarial_loss = nn.BCELoss()(fake_output['real_fake'], real_labels)
        
        # 音乐匹配损失
        music_loss = model._music_matching_loss(fake_charts, audio_features)
        
        # 一致性损失 - 确保生成的谱面在相似音频下保持一致
        consistency_loss = torch.tensor(0.0, device=device)
        if batch_size > 1:
            # 计算音频特征的相似性
            audio_flat = audio_features.view(batch_size, -1)
            audio_sim = torch.mm(audio_flat, audio_flat.t())
            audio_sim = torch.softmax(audio_sim, dim=1)
            
            # 计算生成谱面的相似性
            charts_flat = fake_charts.view(batch_size, -1)
            charts_sim = torch.mm(charts_flat, charts_flat.t())
            charts_sim = torch.softmax(charts_sim, dim=1)
            
            # 一致性损失：相似音频应该生成相似谱面
            consistency_loss = nn.MSELoss()(charts_sim, audio_sim)
        
        # 多样性损失 - 防止模式崩塌
        diversity_loss = torch.tensor(0.0, device=device)
        if batch_size > 1:
            # 计算生成谱面的方差，鼓励多样性
            charts_mean = torch.mean(fake_charts, dim=0, keepdim=True)
            charts_var = torch.mean((fake_charts - charts_mean) ** 2)
            diversity_loss = -torch.log(charts_var + 1e-8)  # 负对数方差
        
        # 总生成器损失
        total_g_loss = (loss_weights['adversarial'] * g_adversarial_loss + 
                        loss_weights['music_matching'] * music_loss +
                        loss_weights['consistency'] * consistency_loss +
                        loss_weights['diversity'] * diversity_loss)
    
    # 生成器反向传播 - 彻底修复：按照PyTorch官方文档
    if use_amp and scaler_g is not None:
        scaler_g.scale(total_g_loss).backward()
        # 关键修复：检查梯度是否有效
        scaler_g.unscale_(optimizer_g)
        # 可选：梯度裁剪
        torch.nn.utils.clip_grad_norm_(model.generator.parameters(), max_norm=1.0)
        scaler_g.step(optimizer_g)
        scaler_g.update()
    else:
        total_g_loss.backward()
        torch.nn.utils.clip_grad_norm_(model.generator.parameters(), max_norm=1.0)
        optimizer_g.step()
    
    return {
        'd_loss': d_loss.item(),
        'g_loss': total_g_loss.item(),
        'adversarial_loss': g_adversarial_loss.item(),
        'music_loss': music_loss.item(),
        'quality_loss': quality_loss.item(),
        'difficulty_loss': difficulty_loss.item(),
        'consistency_loss': consistency_loss.item(),
        'diversity_loss': diversity_loss.item(),
        'gradient_penalty': gradient_penalty.item() if use_wgan else 0.0
    }

def train_mixed_precision_gan(data_dir, save_dir, epochs=5, batch_size=8, lr=0.0002, use_amp=True):
    """优化的GAN训练 - 使用预提取特征和多项优化"""
    device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
    print(f"🚀 使用设备: {device}")
    print(f"⚡ 混合精度训练: {'启用' if use_amp else '禁用'}")

    # 检查是否有预提取的音频特征
    cache_file = Path(data_dir) / "audio_features_cache.pkl"
    if not cache_file.exists():
        print("⚠️  未找到预提取的音频特征缓存")
        print("💡 建议先运行: python preprocess_audio.py --data-dir your_data_dir")
        print("🔄 继续使用实时特征提取...")
    else:
        print(f"✅ 找到音频特征缓存: {cache_file}")

    # 创建数据集
    print("📊 创建数据集...")
    print(f"   - 数据目录: {data_dir}")

    # 创建数据集 - 禁用数据增强以提升速度
    dataset = AudioChartDataset(data_dir, augment=False)
    
    print(f"✅ 数据集创建完成，共 {len(dataset)} 个样本")
    
    # 计算分割大小
    total_size = len(dataset)
    train_size = int(total_size * 0.8)
    val_size = total_size - train_size
    
    print("🔀 分割数据集...")
    print(f"   - 训练集: {train_size} (80.0%)")
    print(f"   - 验证集: {val_size} (20.0%)")
    
    train_dataset, val_dataset = random_split(dataset, [train_size, val_size])

    # 优化DataLoader - 增加num_workers和pin_memory
    num_workers = min(4, batch_size)  # 根据批次大小调整
    train_loader = DataLoader(
        train_dataset,
        batch_size=batch_size,
        shuffle=True,
        num_workers=num_workers,
        pin_memory=True if device.type == 'cuda' else False,
        persistent_workers=True if num_workers > 0 else False
    )
    
    print(f"✅ 训练集: {len(train_dataset)}, 验证集: {len(val_dataset)}")
    
    # 创建模型
    print("🧠 创建模型...")
    model = AudioChartGAN(track_count=4, max_length=2000, use_wgan=True).to(device)  # 启用WGAN
    
    # 优化器
    optimizer_d = optim.Adam(model.discriminator.parameters(), lr=lr, betas=(0.0, 0.9))  # WGAN推荐参数
    optimizer_g = optim.Adam(model.generator.parameters(), lr=lr, betas=(0.0, 0.9))
    
    # 混合精度缩放器 - 为每个优化器创建独立的scaler
    scaler_d = GradScaler('cuda') if use_amp else None
    scaler_g = GradScaler('cuda') if use_amp else None
    
    print(f"🎯 开始训练 {epochs} 轮...")
    
    # 训练循环
    for epoch in range(epochs):
        model.train()
        epoch_d_loss = 0
        epoch_g_loss = 0
        epoch_music_loss = 0
        
        progress_bar = tqdm(train_loader, desc=f"Epoch {epoch+1}/{epochs}")
        
        for batch_idx, batch in enumerate(progress_bar):
            real_charts = batch['chart'].to(device)
            audio_features = batch['audio_features'].to(device)
            target_quality = batch['quality'].to(device)
            target_difficulty = batch['difficulty'].to(device)
            
            # 混合精度训练步骤 - 传入独立的scaler
            losses = mixed_precision_train_step(
                model, real_charts, audio_features, target_quality, target_difficulty,
                optimizer_d, optimizer_g, scaler_d, scaler_g, device, 
                use_wgan=True, use_amp=use_amp
            )
            
            epoch_d_loss += losses['d_loss']
            epoch_g_loss += losses['g_loss']
            epoch_music_loss += losses['music_loss']
            
            # 更新进度条 - 显示WGAN-GP损失
            progress_bar.set_postfix({
                'D_loss': f"{losses['d_loss']:.4f}",
                'G_loss': f"{losses['g_loss']:.4f}",
                'GP': f"{losses['gradient_penalty']:.4f}",
                'Music': f"{losses['music_loss']:.4f}"
            })
        
        # 打印epoch统计
        avg_d_loss = epoch_d_loss / len(train_loader)
        avg_g_loss = epoch_g_loss / len(train_loader)
        avg_music_loss = epoch_music_loss / len(train_loader)
        
        print(f"Epoch {epoch+1} 完成:")
        print(f"  判别器损失: {avg_d_loss:.4f}")
        print(f"  生成器损失: {avg_g_loss:.4f}")
        print(f"  音乐匹配损失: {avg_music_loss:.4f}")
        
        # 保存模型
        if True:
            save_path = Path(save_dir) / f"gan_epoch_{epoch+1}.pth"
            save_path.parent.mkdir(parents=True, exist_ok=True)
            torch.save({
                'epoch': epoch + 1,
                'model_state_dict': model.state_dict(),
                'optimizer_d_state_dict': optimizer_d.state_dict(),
                'optimizer_g_state_dict': optimizer_g.state_dict(),
            }, save_path)
            print(f"💾 模型已保存: {save_path}")
    
    print("🎉 训练完成！")

if __name__ == "__main__":
    parser = argparse.ArgumentParser(description="完整功能GAN训练")
    parser.add_argument("--data-dir", required=True, help="数据目录")
    parser.add_argument("--save-dir", required=True, help="保存目录")
    parser.add_argument("--epochs", type=int, default=5, help="训练轮数")
    parser.add_argument("--batch-size", type=int, default=8, help="批次大小")
    parser.add_argument("--lr", type=float, default=0.0002, help="学习率")
    
    args = parser.parse_args()
    
    try:
        train_mixed_precision_gan(
            data_dir=args.data_dir,
            save_dir=args.save_dir,
            epochs=args.epochs,
            batch_size=args.batch_size,
            lr=args.lr,
            use_amp=False  # 禁用混合精度，保留所有其他高级功能
        )
    except Exception as e:
        logger.error(f"训练失败: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)
