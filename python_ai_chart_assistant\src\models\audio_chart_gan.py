"""
Audio-to-Chart GAN模型

使用对抗网络直接从MP3音频生成音游谱面
"""

import torch
import torch.nn as nn
import torch.nn.functional as F
import librosa
import numpy as np
from typing import Tuple, Optional, Dict, Any
import logging

logger = logging.getLogger(__name__)


class AudioEncoder(nn.Module):
    """音频编码器 - 提取音频特征"""
    
    def __init__(self, input_dim: int = 94, hidden_dim: int = 512):
        super().__init__()
        
        # 音频特征提取层 - 保持时间维度不变
        self.conv_layers = nn.Sequential(
            # 第一层：时间维度卷积
            nn.Conv1d(input_dim, 256, kernel_size=3, padding=1),
            nn.BatchNorm1d(256),
            nn.ReLU(),
            nn.Dropout(0.1),

            # 第二层：特征抽象
            nn.Conv1d(256, 512, kernel_size=3, padding=1),
            nn.BatchNorm1d(512),
            nn.ReLU(),
            nn.Dropout(0.1),

            # 第三层：高级特征
            nn.Conv1d(512, hidden_dim, kernel_size=3, padding=1),
            nn.BatchNorm1d(hidden_dim),
            nn.ReLU(),
        )
        
        # 注意力机制
        self.attention = nn.MultiheadAttention(hidden_dim, num_heads=8)
        
    def forward(self, audio_features: torch.Tensor) -> torch.Tensor:
        """
        前向传播
        
        Args:
            audio_features: [batch, features, time] 音频特征
            
        Returns:
            torch.Tensor: 编码后的音频表示
        """
        # 卷积特征提取
        x = self.conv_layers(audio_features)  # [batch, hidden_dim, time]
        
        # 转换为注意力机制的输入格式
        x = x.permute(2, 0, 1)  # [time, batch, hidden_dim]
        
        # 自注意力
        attended, _ = self.attention(x, x, x)
        
        return attended.permute(1, 0, 2)  # [batch, time, hidden_dim]


class ChartGenerator(nn.Module):
    """谱面生成器 - 从音频特征生成谱面"""
    
    def __init__(self, audio_dim: int = 512, track_count: int = 4, max_length: int = 2000):
        super().__init__()
        
        self.track_count = track_count
        self.max_length = max_length
        
        # 音频编码器
        self.audio_encoder = AudioEncoder(input_dim=94, hidden_dim=audio_dim)
        
        # 谱面生成网络
        self.chart_generator = nn.Sequential(
            nn.Linear(audio_dim, 1024),
            nn.ReLU(),
            nn.Dropout(0.3),
            
            nn.Linear(1024, 512),
            nn.ReLU(),
            nn.Dropout(0.3),
            
            # 输出层：每个时间步的每个轨道的音符概率
            nn.Linear(512, track_count * 3)  # 3种音符类型：空白、短音符、长音符
        )
        
        # LSTM用于时序建模
        self.lstm = nn.LSTM(
            input_size=audio_dim,
            hidden_size=256,
            num_layers=2,
            batch_first=True,
            dropout=0.2,
            bidirectional=True
        )
        
        # 最终输出层
        self.output_layer = nn.Linear(512, track_count * 3)
        
    def forward(self, audio_features: torch.Tensor) -> torch.Tensor:
        """
        生成谱面
        
        Args:
            audio_features: [batch, features, time] 音频特征
            
        Returns:
            torch.Tensor: [batch, time, tracks*3] 谱面概率
        """
        # 编码音频特征
        encoded = self.audio_encoder(audio_features)  # [batch, time, hidden_dim]
        
        # LSTM时序建模
        lstm_out, _ = self.lstm(encoded)  # [batch, time, 512]
        
        # 生成谱面
        chart_logits = self.output_layer(lstm_out)  # [batch, time, tracks*3]
        
        # 重塑为 [batch, time, tracks, 3]
        batch_size, time_steps = chart_logits.shape[:2]
        chart_logits = chart_logits.view(batch_size, time_steps, self.track_count, 3)
        
        # 应用softmax获得概率分布
        chart_probs = F.softmax(chart_logits, dim=-1)
        
        return chart_probs


class ChartDiscriminator(nn.Module):
    """谱面判别器 - 判断谱面质量和真实性"""
    
    def __init__(self, track_count: int = 4, max_length: int = 2000):
        super().__init__()
        
        self.track_count = track_count
        
        # 谱面特征提取
        self.conv_layers = nn.Sequential(
            # 第一层：局部模式识别
            nn.Conv2d(1, 64, kernel_size=(3, track_count), padding=(1, 0)),
            nn.BatchNorm2d(64),
            nn.LeakyReLU(0.2),
            
            # 第二层：时序模式
            nn.Conv2d(64, 128, kernel_size=(5, 1), padding=(2, 0)),
            nn.BatchNorm2d(128),
            nn.LeakyReLU(0.2),
            nn.MaxPool2d((2, 1)),
            
            # 第三层：复杂模式
            nn.Conv2d(128, 256, kernel_size=(3, 1), padding=(1, 0)),
            nn.BatchNorm2d(256),
            nn.LeakyReLU(0.2),
            nn.MaxPool2d((2, 1)),
        )
        
        # 全连接层（使用自适应池化来处理不同的输入尺寸）
        self.adaptive_pool = nn.AdaptiveAvgPool2d((1, 1))  # 将任意尺寸池化为1x1
        self.fc_layers = nn.Sequential(
            nn.Linear(256, 512),  # 256是最后一个卷积层的通道数
            nn.LeakyReLU(0.2),
            nn.Dropout(0.5),

            nn.Linear(512, 128),
            nn.LeakyReLU(0.2),
            nn.Dropout(0.5),

            # 多任务输出
            nn.Linear(128, 64),
            nn.LeakyReLU(0.2),
        )
        
        # 输出头
        self.real_fake_head = nn.Linear(64, 1)  # 真假判别
        self.quality_head = nn.Linear(64, 1)    # 质量评分
        self.difficulty_head = nn.Linear(64, 10) # 难度分类
        
    def forward(self, chart: torch.Tensor) -> Dict[str, torch.Tensor]:
        """
        判别谱面
        
        Args:
            chart: [batch, time, tracks] 谱面数据
            
        Returns:
            Dict: 包含各种判别结果
        """
        batch_size = chart.shape[0]
        
        # 添加通道维度 [batch, 1, time, tracks]
        x = chart.unsqueeze(1)
        
        # 卷积特征提取
        x = self.conv_layers(x)  # [batch, 256, time//4, 1]

        # 使用自适应池化处理不同的时间长度
        x = self.adaptive_pool(x)  # [batch, 256, 1, 1]

        # 展平
        x = x.view(batch_size, -1)  # [batch, 256]

        # 全连接层
        features = self.fc_layers(x)  # [batch, 64]
        
        # 恢复多任务输出 - 使用独立特征副本避免梯度冲突
        real_fake_logits = self.real_fake_head(features)
        real_fake = torch.sigmoid(real_fake_logits)

        # 为质量和难度预测创建独立的特征副本
        quality_features = features.detach().clone().requires_grad_(True)
        quality_logits = self.quality_head(quality_features)
        quality = torch.sigmoid(quality_logits) * 10  # 0-10分

        difficulty_features = features.detach().clone().requires_grad_(True)
        difficulty_logits = self.difficulty_head(difficulty_features)
        difficulty = F.softmax(difficulty_logits, dim=1)

        return {
            'real_fake': real_fake,
            'quality': quality,
            'difficulty': difficulty
        }

    def get_features(self, chart: torch.Tensor) -> torch.Tensor:
        """
        获取中间特征用于特征匹配损失

        Args:
            chart: [batch, time, tracks] 谱面数据

        Returns:
            torch.Tensor: 中间特征 [batch, 64]
        """
        batch_size = chart.shape[0]

        # 添加通道维度
        x = chart.unsqueeze(1)

        # 卷积特征提取
        x = self.conv_layers(x)

        # 自适应池化
        x = self.adaptive_pool(x)

        # 展平
        x = x.view(batch_size, -1)

        # 全连接层得到特征
        features = self.fc_layers(x)

        return features


class AudioChartGAN(nn.Module):
    """完整的Audio-to-Chart GAN模型 - 优化版本"""

    def __init__(self, track_count: int = 4, max_length: int = 2000, use_wgan: bool = True):
        super().__init__()

        self.generator = ChartGenerator(track_count=track_count, max_length=max_length)
        self.discriminator = ChartDiscriminator(track_count=track_count, max_length=max_length)
        self.track_count = track_count
        self.use_wgan = use_wgan

        # 损失函数
        if use_wgan:
            # WGAN不使用sigmoid，直接输出logits
            self.adversarial_loss = self._wasserstein_loss
        else:
            self.adversarial_loss = nn.BCELoss()

        self.quality_loss = nn.MSELoss()
        self.difficulty_loss = nn.CrossEntropyLoss()
        self.music_matching_loss = nn.MSELoss()  # 新增：音乐匹配损失

        # 损失权重
        self.loss_weights = {
            'adversarial': 1.0,
            'quality': 0.5,
            'difficulty': 0.3,
            'music_matching': 0.8,
            'gradient_penalty': 10.0  # WGAN-GP
        }
        
    def extract_audio_features(self, audio_path: str) -> torch.Tensor:
        """
        从MP3文件提取音频特征（与训练时保持一致）

        Args:
            audio_path: MP3文件路径

        Returns:
            torch.Tensor: 音频特征 [1, features, time]
        """
        # 加载音频
        y, sr = librosa.load(audio_path, sr=22050)

        # 提取多种特征（与训练时保持一致）
        features = []

        # 1. Mel频谱图 (64维)
        mel_spec = librosa.feature.melspectrogram(y=y, sr=sr, n_mels=64, hop_length=512)
        mel_spec_db = librosa.power_to_db(mel_spec, ref=np.max)
        features.append(mel_spec_db)

        # 2. MFCC (13维)
        mfcc = librosa.feature.mfcc(y=y, sr=sr, n_mfcc=13, hop_length=512)
        features.append(mfcc)

        # 3. 色度特征 (12维)
        chroma = librosa.feature.chroma_stft(y=y, sr=sr, hop_length=512)
        features.append(chroma)

        # 4. 频谱质心 (1维)
        spectral_centroids = librosa.feature.spectral_centroid(y=y, sr=sr, hop_length=512)
        features.append(spectral_centroids)

        # 5. 频谱带宽 (1维)
        spectral_bandwidth = librosa.feature.spectral_bandwidth(y=y, sr=sr, hop_length=512)
        features.append(spectral_bandwidth)

        # 6. 频谱滚降 (1维)
        spectral_rolloff = librosa.feature.spectral_rolloff(y=y, sr=sr, hop_length=512)
        features.append(spectral_rolloff)

        # 7. 零交叉率 (1维)
        zcr = librosa.feature.zero_crossing_rate(y, hop_length=512)
        features.append(zcr)

        # 8. 节拍跟踪 (1维)
        tempo, beats = librosa.beat.beat_track(y=y, sr=sr, hop_length=512)
        beat_features = np.zeros((1, mel_spec.shape[1]))
        beat_frames = librosa.time_to_frames(librosa.frames_to_time(beats), sr=sr, hop_length=512)
        for beat_frame in beat_frames:
            if beat_frame < beat_features.shape[1]:
                beat_features[0, beat_frame] = 1.0
        features.append(beat_features)

        # 合并所有特征
        combined_features = np.vstack(features)  # [92, time]

        # 转换为tensor
        return torch.FloatTensor(combined_features).unsqueeze(0)  # [1, features, time]

    def _wasserstein_loss(self, real_output: torch.Tensor, fake_output: torch.Tensor) -> torch.Tensor:
        """Wasserstein损失函数"""
        return torch.mean(fake_output) - torch.mean(real_output)

    def _gradient_penalty(self, real_data: torch.Tensor, fake_data: torch.Tensor) -> torch.Tensor:
        """梯度惩罚 - WGAN-GP的关键组件"""
        batch_size = real_data.size(0)
        device = real_data.device

        # 随机插值 - 确保创建新的张量
        alpha = torch.rand(batch_size, 1, 1).to(device)
        alpha = alpha.expand_as(real_data)

        # 使用detach()确保不会影响原始张量的梯度
        interpolated = alpha * real_data.detach() + (1 - alpha) * fake_data.detach()
        interpolated = interpolated.clone()  # 创建新的张量副本
        interpolated.requires_grad_(True)

        # 计算判别器输出 - 确保使用新的前向传播
        with torch.enable_grad():
            disc_output = self.discriminator(interpolated)['real_fake']

        # 计算梯度
        gradients = torch.autograd.grad(
            outputs=disc_output,
            inputs=interpolated,
            grad_outputs=torch.ones_like(disc_output),
            create_graph=True,
            retain_graph=True,
            only_inputs=True
        )[0]

        # 梯度惩罚
        gradients = gradients.view(batch_size, -1)
        gradient_penalty = ((gradients.norm(2, dim=1) - 1) ** 2).mean()

        return gradient_penalty

    def _music_matching_loss(self, generated_chart: torch.Tensor, audio_features: torch.Tensor) -> torch.Tensor:
        """音乐匹配损失 - 确保生成的谱面与音乐节拍匹配"""
        # 提取音频的节拍强度
        beat_strength = torch.mean(audio_features[:, :13, :], dim=1)  # 使用MFCC的平均值作为节拍强度

        # 计算谱面的音符密度
        chart_density = torch.sum(generated_chart, dim=-1)  # [batch, time]

        # 调整尺寸匹配
        min_length = min(beat_strength.size(-1), chart_density.size(-1))
        beat_strength = beat_strength[:, :min_length]
        chart_density = chart_density[:, :min_length]

        # 计算相关性损失
        return self.music_matching_loss(chart_density, beat_strength)
    
    def train_step(self, real_charts: torch.Tensor, audio_features: torch.Tensor,
                   target_difficulty: torch.Tensor, target_quality: torch.Tensor) -> Dict[str, float]:
        """
        训练步骤 - 优化版本

        Args:
            real_charts: 真实谱面 [batch, time, tracks]
            audio_features: 音频特征 [batch, features, time]
            target_difficulty: 目标难度 [batch]
            target_quality: 目标质量 [batch]

        Returns:
            Dict[str, float]: 各种损失值
        """
        batch_size = real_charts.size(0)
        device = real_charts.device

        # 生成假谱面
        fake_charts_probs = self.generator(audio_features)  # [batch, time, tracks, 3]
        fake_charts = self._constrained_sampling(fake_charts_probs)  # [batch, time, tracks]

        # 判别器输出 - 确保张量独立
        real_output = self.discriminator(real_charts.clone())
        fake_output = self.discriminator(fake_charts.detach().clone())

        losses = {}

        if self.use_wgan:
            # WGAN损失
            d_loss = torch.mean(fake_output['real_fake']) - torch.mean(real_output['real_fake'])

            # 梯度惩罚
            gp = self._gradient_penalty(real_charts, fake_charts)
            d_loss += self.loss_weights['gradient_penalty'] * gp

            losses['discriminator_loss'] = d_loss.item()
            losses['gradient_penalty'] = gp.item()
        else:
            # 标准GAN损失
            real_labels = torch.ones(batch_size, 1).to(device)
            fake_labels = torch.zeros(batch_size, 1).to(device)

            d_real_loss = self.adversarial_loss(real_output['real_fake'], real_labels)
            d_fake_loss = self.adversarial_loss(fake_output['real_fake'], fake_labels)
            d_loss = (d_real_loss + d_fake_loss) / 2

            losses['discriminator_loss'] = d_loss.item()

        # 质量损失 - 确保张量形状匹配
        quality_pred = real_output['quality']  # [batch, 1]
        quality_target = target_quality.float()
        if quality_target.dim() == 1:
            quality_target = quality_target.unsqueeze(1)  # [batch, 1]
        quality_loss = self.quality_loss(quality_pred, quality_target)
        losses['quality_loss'] = quality_loss.item()

        # 难度损失 - 使用MSE损失而不是交叉熵，因为难度现在是连续值
        # 确保张量形状匹配
        difficulty_pred = real_output['difficulty']  # [batch, 10]
        difficulty_target = target_difficulty.float().unsqueeze(1)  # [batch, 1]

        # 将预测转换为单一值（使用加权平均）
        difficulty_weights = torch.arange(10, dtype=torch.float32, device=difficulty_pred.device)
        difficulty_pred_value = torch.sum(difficulty_pred * difficulty_weights, dim=1, keepdim=True)  # [batch, 1]

        difficulty_loss = self.quality_loss(difficulty_pred_value, difficulty_target)
        losses['difficulty_loss'] = difficulty_loss.item()

        # 生成器损失 - 重新生成以避免梯度冲突
        fake_charts_for_gen_probs = self.generator(audio_features)
        fake_charts_for_gen = self._constrained_sampling(fake_charts_for_gen_probs)
        fake_output_for_gen = self.discriminator(fake_charts_for_gen)

        if self.use_wgan:
            g_loss = -torch.mean(fake_output_for_gen['real_fake'])
        else:
            real_labels = torch.ones(batch_size, 1).to(device)
            g_loss = self.adversarial_loss(fake_output_for_gen['real_fake'], real_labels)

        # 音乐匹配损失
        music_loss = self._music_matching_loss(fake_charts_for_gen, audio_features)

        # 总生成器损失
        total_g_loss = (self.loss_weights['adversarial'] * g_loss +
                       self.loss_weights['music_matching'] * music_loss)

        losses['generator_loss'] = g_loss.item()
        losses['music_matching_loss'] = music_loss.item()
        losses['total_generator_loss'] = total_g_loss.item()

        return losses, d_loss, total_g_loss

    def generate_chart(self, audio_path: str, difficulty: int = 5) -> np.ndarray:
        """
        从MP3文件生成谱面 - 优化版本

        Args:
            audio_path: MP3文件路径
            difficulty: 目标难度 (1-10)

        Returns:
            np.ndarray: 生成的谱面
        """
        self.eval()
        with torch.no_grad():
            # 提取音频特征
            audio_features = self.extract_audio_features(audio_path)

            # 确保特征在正确的设备上
            device = next(self.parameters()).device
            audio_features = audio_features.to(device)

            # 生成谱面
            chart_probs = self.generator(audio_features)  # [1, time, tracks, 3]

            # 使用改进的约束采样
            chart = self._improved_constrained_sampling(chart_probs, difficulty)  # [1, time, tracks]

            return chart.squeeze(0).cpu().numpy()  # [time, tracks]

    def _constrained_sampling(self, chart_probs: torch.Tensor) -> torch.Tensor:
        """
        约束采样，避免音符重叠

        Args:
            chart_probs: [batch, time, tracks, 3] 谱面概率

        Returns:
            torch.Tensor: [batch, time, tracks] 采样后的谱面
        """
        batch_size, time_steps, num_tracks, _ = chart_probs.shape
        chart = torch.zeros(batch_size, time_steps, num_tracks, dtype=torch.float32, device=chart_probs.device)

        # 对每个批次和轨道单独处理
        for batch in range(batch_size):
            for track in range(num_tracks):
                track_probs = chart_probs[batch, :, track, :]  # [time, 3]

                # 跟踪当前活跃的长音符
                active_hold_end = -1  # 当前长音符的结束时间

                for t in range(time_steps):
                    probs = track_probs[t].clone()  # [3]

                    # 如果当前有活跃的长音符，禁止新音符
                    if t < active_hold_end:
                        probs[1] = 0  # 禁止短音符
                        probs[2] = 0  # 禁止长音符
                        probs[0] = 1  # 强制空音符

                    # 重新归一化概率
                    probs = probs / probs.sum()

                    # 采样
                    note_type = torch.multinomial(probs, 1).item()
                    chart[batch, t, track] = note_type

                    # 如果是长音符，更新活跃状态
                    if note_type == 2:  # 长音符
                        # 假设长音符持续5个时间步
                        hold_duration = 5
                        active_hold_end = t + hold_duration

        return chart

    def _improved_constrained_sampling(self, chart_probs: torch.Tensor, difficulty: int = 5) -> torch.Tensor:
        """
        改进的约束采样 - 考虑难度和音乐性

        Args:
            chart_probs: [batch, time, tracks, 3] 概率分布
            difficulty: 目标难度 (1-10)

        Returns:
            torch.Tensor: [batch, time, tracks] 采样结果
        """
        batch_size, time_steps, tracks, _ = chart_probs.shape
        device = chart_probs.device

        # 根据难度调整采样策略
        note_density_target = difficulty * 0.1  # 难度越高，音符密度越大

        chart = torch.zeros(batch_size, time_steps, tracks, dtype=torch.float32).to(device)

        for b in range(batch_size):
            current_density = 0.0
            consecutive_empty = 0

            for t in range(time_steps):
                # 计算当前时间步的音符概率
                probs = chart_probs[b, t]  # [tracks, 3]

                # 根据当前密度调整概率
                if current_density < note_density_target:
                    # 密度不够，增加音符概率
                    probs[:, 1:] *= 1.5  # 增加非空音符概率
                elif current_density > note_density_target * 1.2:
                    # 密度过高，减少音符概率
                    probs[:, 0] *= 1.5  # 增加空白概率

                # 避免连续太多空白
                if consecutive_empty > 4:
                    probs[:, 1:] *= 2.0

                # 重新归一化
                probs = F.softmax(probs, dim=-1)

                # 采样
                _, indices = torch.multinomial(probs.view(-1, 3), 1).view(tracks).max(dim=-1)

                # 应用约束和更新统计
                has_note = False
                for track in range(tracks):
                    note_type = indices[track].item()
                    if note_type == 1:  # 短音符
                        chart[b, t, track] = 1
                        has_note = True
                    elif note_type == 2:  # 长音符
                        chart[b, t, track] = 2
                        has_note = True

                # 更新统计
                if has_note:
                    current_density = (current_density * t + 1) / (t + 1)
                    consecutive_empty = 0
                else:
                    current_density = (current_density * t) / (t + 1)
                    consecutive_empty += 1

        return chart
