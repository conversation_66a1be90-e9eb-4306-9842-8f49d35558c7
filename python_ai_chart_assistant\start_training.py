#!/usr/bin/env python3
"""
训练启动脚本

一键启动训练流程，包括数据预处理和模型训练
"""

import argparse
import subprocess
import sys
from pathlib import Path
import logging

from src.utils.logger_setup import setup_logging

logger = logging.getLogger(__name__)


def parse_args():
    """解析命令行参数"""
    parser = argparse.ArgumentParser(description='启动AI谱面生成模型训练')
    
    # 数据参数
    parser.add_argument('--chart-dir', type=str, default='谱面文件',
                       help='谱面文件目录路径')
    parser.add_argument('--audio-dir', type=str, default=None,
                       help='音频文件目录路径（可选）')
    parser.add_argument('--data-dir', type=str, default='data/processed',
                       help='处理后的数据目录')
    
    # 训练参数
    parser.add_argument('--epochs', type=int, default=50,
                       help='训练轮数')
    parser.add_argument('--batch-size', type=int, default=8,
                       help='批次大小')
    parser.add_argument('--lr-g', type=float, default=0.0002,
                       help='生成器学习率')
    parser.add_argument('--lr-d', type=float, default=0.0002,
                       help='判别器学习率')
    
    # 模型参数
    parser.add_argument('--track-count', type=int, default=4,
                       help='轨道数量')
    parser.add_argument('--max-length', type=int, default=2000,
                       help='最大序列长度')
    
    # 设备参数
    parser.add_argument('--device', type=str, default='auto',
                       choices=['auto', 'cpu', 'cuda'],
                       help='计算设备')
    
    # 其他参数
    parser.add_argument('--skip-preprocessing', action='store_true',
                       help='跳过数据预处理步骤')
    parser.add_argument('--resume', type=str, default=None,
                       help='恢复训练的模型路径')
    parser.add_argument('--log-level', type=str, default='INFO',
                       choices=['DEBUG', 'INFO', 'WARNING', 'ERROR'],
                       help='日志级别')
    
    return parser.parse_args()


def run_preprocessing(args):
    """运行数据预处理"""
    print("开始数据预处理...")

    cmd = [
        sys.executable, 'prepare_training_data.py',
        '--chart-dir', args.chart_dir,
        '--output-dir', args.data_dir,
        '--log-level', args.log_level
    ]

    if args.audio_dir:
        cmd.extend(['--audio-dir', args.audio_dir])

    try:
        # 设置子进程环境变量
        import os
        env = os.environ.copy()
        env['PYTHONIOENCODING'] = 'utf-8'
        env['PYTHONUTF8'] = '1'

        result = subprocess.run(cmd, check=True, capture_output=True, text=True,
                              encoding='utf-8', env=env)
        print("数据预处理完成")
        return True
    except subprocess.CalledProcessError as e:
        print(f"数据预处理失败: {e}")
        print(f"错误输出: {e.stderr}")
        return False


def run_training(args):
    """运行模型训练"""
    print("开始模型训练...")

    cmd = [
        sys.executable, 'train_gan.py',
        '--data-dir', args.data_dir,
        '--epochs', str(args.epochs),
        '--batch-size', str(args.batch_size),
        '--lr-g', str(args.lr_g),
        '--lr-d', str(args.lr_d),
        '--track-count', str(args.track_count),
        '--max-length', str(args.max_length),
        '--device', args.device,
        '--log-level', args.log_level
    ]

    if args.resume:
        cmd.extend(['--resume', args.resume])

    try:
        # 设置子进程环境变量
        import os
        env = os.environ.copy()
        env['PYTHONIOENCODING'] = 'utf-8'
        env['PYTHONUTF8'] = '1'

        # 实时显示训练输出
        process = subprocess.Popen(cmd, stdout=subprocess.PIPE, stderr=subprocess.STDOUT,
                                 text=True, bufsize=1, universal_newlines=True,
                                 encoding='utf-8', env=env)

        for line in process.stdout:
            print(line.rstrip())

        process.wait()

        if process.returncode == 0:
            print("模型训练完成")
            return True
        else:
            print(f"模型训练失败，退出码: {process.returncode}")
            return False

    except Exception as e:
        print(f"训练过程出错: {e}")
        return False


def check_dependencies():
    """检查依赖项"""
    print("检查依赖项...")

    required_packages = [
        'torch', 'librosa', 'numpy', 'soundfile'
    ]

    missing_packages = []

    for package in required_packages:
        try:
            __import__(package)
        except ImportError:
            missing_packages.append(package)

    if missing_packages:
        print(f"缺少依赖包: {', '.join(missing_packages)}")
        print("请运行以下命令安装依赖:")
        print(f"pip install {' '.join(missing_packages)}")
        return False

    print("依赖项检查通过")
    return True


def check_data_directory(args):
    """检查数据目录"""
    chart_dir = Path(args.chart_dir)
    
    if not chart_dir.exists():
        print(f"谱面目录不存在: {chart_dir}")
        return False
    
    # 检查是否有谱面文件（递归搜索子文件夹）
    chart_files = []
    extensions = ['.imd']  # 只处理IMD格式

    print(f"递归搜索谱面文件...")
    for ext in extensions:
        # 使用 ** 进行递归搜索
        found_files = list(chart_dir.rglob(f"*{ext}"))
        chart_files.extend(found_files)
        if found_files:
            print(f"  找到 {len(found_files)} 个 {ext} 文件")

    if not chart_files:
        print(f"在谱面目录中未找到支持的谱面文件: {chart_dir}")
        print(f"支持的格式: {', '.join(extensions)}")
        print(f"提示: 已递归搜索所有子文件夹")
        return False

    print(f"总共找到 {len(chart_files)} 个谱面文件")
    
    # 检查音频目录
    if args.audio_dir:
        audio_dir = Path(args.audio_dir)
        if not audio_dir.exists():
            print(f"音频目录不存在: {audio_dir}")
            args.audio_dir = None
        else:
            audio_files = []
            audio_extensions = ['.mp3', '.wav', '.flac', '.m4a', '.ogg']
            for ext in audio_extensions:
                audio_files.extend(audio_dir.glob(f"*{ext}"))
            print(f"找到 {len(audio_files)} 个音频文件")
    
    return True


def main():
    """主函数"""
    args = parse_args()

    # 设置环境变量以支持中文输出
    import os
    os.environ['PYTHONIOENCODING'] = 'utf-8'
    os.environ['PYTHONUTF8'] = '1'
    if sys.platform.startswith('win'):
        os.system('chcp 65001 >nul 2>&1')

    # 设置日志
    setup_logging(level=args.log_level)

    print("AI音游谱面生成模型训练器")
    print("=" * 50)

    # 检查依赖项
    if not check_dependencies():
        return 1
    
    # 检查数据目录
    if not check_data_directory(args):
        return 1
    
    # 数据预处理
    if not args.skip_preprocessing:
        if not run_preprocessing(args):
            return 1
    else:
        print("跳过数据预处理")

        # 检查处理后的数据是否存在
        data_dir = Path(args.data_dir)
        if not data_dir.exists() or not (data_dir / "audio").exists() or not (data_dir / "charts").exists():
            print(f"处理后的数据目录不存在或不完整: {data_dir}")
            print("请先运行数据预处理或移除 --skip-preprocessing 参数")
            return 1

    # 模型训练
    if not run_training(args):
        return 1

    print("\n训练流程完成！")
    print("训练好的模型保存在 models/ 目录中")
    print("您可以使用以下命令测试模型:")
    print("python -m src.cli generate input.mid output.mc --difficulty 6")
    
    return 0


if __name__ == "__main__":
    exit(main())
