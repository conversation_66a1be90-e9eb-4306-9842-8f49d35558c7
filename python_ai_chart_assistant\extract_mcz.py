#!/usr/bin/env python3
"""
简化版MCZ文件批量解压器

专门用于解压节奏大师的.mcz格式文件
"""

import os
import zipfile
import logging
from pathlib import Path
from typing import List

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(message)s')
logger = logging.getLogger(__name__)

def extract_mcz_files(source_dir: str = "谱面文件", extract_dir: str = None):
    """
    批量解压MCZ文件
    
    Args:
        source_dir: 源文件夹路径
        extract_dir: 解压目标文件夹路径
    """
    source_path = Path(source_dir)
    extract_path = Path(extract_dir) if extract_dir else source_path / "解压"
    
    if not source_path.exists():
        logger.error(f"❌ 源文件夹不存在: {source_path}")
        return
    
    # 查找所有.mcz文件
    mcz_files = []
    for file_path in source_path.iterdir():
        if file_path.is_file() and file_path.suffix.lower() == '.mcz':
            # 跳过正在下载的文件
            if '.downloading' not in file_path.name:
                mcz_files.append(file_path)
    
    if not mcz_files:
        logger.warning("⚠️ 没有找到.mcz文件")
        return
    
    logger.info(f"🔍 找到 {len(mcz_files)} 个.mcz文件")
    logger.info(f"📁 解压目录: {extract_path}")
    
    # 创建解压目录
    extract_path.mkdir(parents=True, exist_ok=True)
    
    # 统计信息
    success_count = 0
    failed_count = 0
    
    # 批量解压
    for i, mcz_file in enumerate(mcz_files, 1):
        logger.info(f"📦 [{i}/{len(mcz_files)}] 解压: {mcz_file.name}")
        
        # 创建目标文件夹
        target_dir = extract_path / mcz_file.stem
        
        # 如果目标文件夹已存在，跳过
        if target_dir.exists():
            logger.info(f"⏭️ 已存在，跳过: {mcz_file.stem}")
            continue
        
        try:
            # 创建目标文件夹
            target_dir.mkdir(parents=True, exist_ok=True)
            
            # 解压MCZ文件
            with zipfile.ZipFile(mcz_file, 'r') as zip_ref:
                zip_ref.extractall(target_dir)
            
            logger.info(f"✅ 解压成功: {mcz_file.stem}")
            success_count += 1
            
        except zipfile.BadZipFile:
            logger.error(f"❌ 损坏的文件: {mcz_file.name}")
            failed_count += 1
            # 删除创建的空文件夹
            if target_dir.exists():
                target_dir.rmdir()
        except Exception as e:
            logger.error(f"❌ 解压失败: {mcz_file.name} - {e}")
            failed_count += 1
            # 删除创建的空文件夹
            if target_dir.exists() and not any(target_dir.iterdir()):
                target_dir.rmdir()
    
    # 输出统计信息
    logger.info("=" * 50)
    logger.info("📊 批量解压完成！")
    logger.info(f"✅ 成功解压: {success_count} 个文件")
    logger.info(f"❌ 解压失败: {failed_count} 个文件")
    logger.info(f"📈 成功率: {success_count}/{len(mcz_files)} ({success_count/len(mcz_files)*100:.1f}%)")
    logger.info(f"📁 解压文件位于: {extract_path}")
    logger.info("=" * 50)

def main():
    """主函数"""
    print("🎵 MCZ文件批量解压器")
    print("=" * 50)
    
    try:
        extract_mcz_files()
        print("\n🎉 解压完成！")
    except KeyboardInterrupt:
        print("\n⏹️ 用户中断操作")
    except Exception as e:
        print(f"\n❌ 解压过程中出错: {e}")

if __name__ == "__main__":
    main()
