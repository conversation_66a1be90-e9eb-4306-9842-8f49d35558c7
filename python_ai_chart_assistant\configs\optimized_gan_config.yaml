# 优化的GAN训练配置
model:
  type: "audio_chart_gan"
  use_wgan: false  # 暂时使用标准GAN避免梯度惩罚问题
  track_count: 4
  max_length: 2000
  
  # 生成器配置
  generator:
    audio_dim: 512
    hidden_dim: 512
    lstm_layers: 2
    lstm_hidden: 256
    dropout: 0.2
    bidirectional: true
    
  # 判别器配置  
  discriminator:
    conv_channels: [64, 128, 256]
    fc_hidden: [512, 128, 64]
    dropout: 0.5
    leaky_relu_slope: 0.2

# 训练配置
training:
  batch_size: 16  # 增加批次大小提升训练效率
  epochs: 200
  
  # 学习率
  generator_lr: 0.0001
  discriminator_lr: 0.0004  # 判别器学习率稍高
  
  # 优化器
  optimizer: "adam"
  beta1: 0.5
  beta2: 0.999
  weight_decay: 0.00001
  
  # 学习率调度
  scheduler:
    type: "reduce_on_plateau"
    patience: 10
    factor: 0.5
    min_lr: 0.000001
  
  # 梯度裁剪
  grad_clip: 1.0
  
  # 训练策略
  d_steps: 1  # 每轮训练判别器的步数
  g_steps: 1  # 每轮训练生成器的步数
  warmup_epochs: 10  # 预热轮数

# 损失函数权重
loss_weights:
  adversarial: 1.0
  quality: 0.5
  difficulty: 0.3
  music_matching: 0.8
  gradient_penalty: 10.0  # WGAN-GP梯度惩罚权重

# 数据配置
data:
  train_split: 0.8
  val_split: 0.15
  test_split: 0.05
  
  # 数据增强 - 禁用以提升训练速度
  augmentation:
    enabled: false  # 禁用数据增强提升速度
    tempo_shift: 0.1  # 节拍偏移
    pitch_shift: 2    # 音高偏移（半音）
    noise_level: 0.01 # 噪声水平
    
  # 音频特征
  audio_features:
    sr: 22050
    hop_length: 512
    n_mels: 64
    n_mfcc: 13
    n_chroma: 12

# 验证和保存
validation:
  frequency: 10  # 减少验证频率提升训练速度
  metrics: ["loss", "quality_score", "difficulty_accuracy"]
  
save:
  frequency: 10  # 每10个epoch保存一次
  keep_best: true
  keep_last: 3  # 保留最近3个检查点

# 日志配置
logging:
  level: "INFO"
  tensorboard: true
  wandb: false  # 可选：使用Weights & Biases
  
  # TensorBoard配置
  tensorboard:
    log_dir: "logs/tensorboard"
    log_frequency: 100  # 每100步记录一次
    
# 设备配置
device:
  use_cuda: true
  cuda_device: 0
  mixed_precision: false  # 暂时禁用混合精度训练

# 性能优化配置
performance:
  # DataLoader优化
  num_workers: 4  # 数据加载线程数
  pin_memory: true  # 固定内存提升GPU传输速度
  persistent_workers: true  # 保持工作进程活跃

  # 音频特征缓存
  use_audio_cache: true  # 使用预提取的音频特征
  cache_file: "audio_features_cache.pkl"

  # 内存优化
  empty_cache_frequency: 10  # 每10个batch清理一次显存
  gradient_accumulation_steps: 1  # 梯度累积步数

  # 编译优化
  torch_compile: false  # PyTorch 2.0编译优化（实验性）
  channels_last: false  # 使用channels_last内存格式

# 早停配置
early_stopping:
  enabled: true
  patience: 20
  min_delta: 0.001
  monitor: "val_loss"

# 生成配置
generation:
  temperature: 0.8  # 采样温度
  top_k: 50        # Top-K采样
  top_p: 0.9       # Top-P采样
  
  # 约束参数
  max_consecutive_empty: 4
  min_note_gap: 0.1  # 最小音符间隔（秒）
  max_note_density: 0.8  # 最大音符密度

# 评估配置
evaluation:
  metrics:
    - "note_accuracy"
    - "rhythm_consistency" 
    - "difficulty_match"
    - "music_alignment"
    - "playability_score"
  
  # 人工评估
  human_eval:
    enabled: false
    sample_size: 50
    criteria: ["fun", "difficulty", "musicality"]
