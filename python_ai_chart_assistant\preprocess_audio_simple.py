#!/usr/bin/env python3
"""
简化的音频特征预提取脚本 - 避免librosa版本兼容性问题
只使用最基本和稳定的特征
"""

import sys
from pathlib import Path
sys.path.insert(0, str(Path(__file__).parent))

import torch
import numpy as np
from tqdm import tqdm
import librosa
import pickle

def extract_simple_audio_features(audio_path: str, target_length: int = 2000) -> torch.Tensor:
    """
    提取简化的音频特征 - 只使用最稳定的API
    
    Args:
        audio_path: 音频文件路径
        target_length: 目标长度
        
    Returns:
        torch.Tensor: 音频特征 [features, time]
    """
    try:
        # 加载音频
        y, sr = librosa.load(audio_path, sr=22050)
        
        # 如果音频太短，填充零
        if len(y) < sr:  # 少于1秒
            y = np.pad(y, (0, sr - len(y)), mode='constant')
        
        # 提取基本特征
        features = []
        hop_length = 512
        
        # 1. MFCC特征 (13维) - 最稳定的特征
        mfcc = librosa.feature.mfcc(y=y, sr=sr, n_mfcc=13, hop_length=hop_length)
        features.append(mfcc)
        
        # 2. 梅尔频谱 (64维) - 稳定的特征
        mel_spec = librosa.feature.melspectrogram(y=y, sr=sr, n_mels=64, hop_length=hop_length)
        mel_spec_db = librosa.power_to_db(mel_spec, ref=np.max)
        features.append(mel_spec_db)
        
        # 3. 零交叉率 (1维) - 简单稳定
        zcr = librosa.feature.zero_crossing_rate(y, hop_length=hop_length)
        features.append(zcr)
        
        # 4. RMS能量 (1维) - 简单稳定
        rms = librosa.feature.rms(y=y, hop_length=hop_length)
        features.append(rms)
        
        # 5. 光谱质心 (1维) - 通常稳定
        try:
            spectral_centroids = librosa.feature.spectral_centroid(y=y, sr=sr, hop_length=hop_length)
            features.append(spectral_centroids)
        except:
            # 如果失败，用零填充
            spectral_centroids = np.zeros((1, mfcc.shape[1]))
            features.append(spectral_centroids)
        
        # 6. 简化的色度特征 - 使用最基本的方法
        try:
            # 计算短时傅里叶变换
            stft = librosa.stft(y, hop_length=hop_length)
            # 从STFT计算色度
            chroma = librosa.feature.chroma_stft(S=np.abs(stft), sr=sr)
            features.append(chroma)
        except:
            # 如果失败，创建12维零特征
            chroma = np.zeros((12, mfcc.shape[1]))
            features.append(chroma)
        
        # 合并所有特征 - 总共 13+64+1+1+1+12 = 92维
        audio_features = np.concatenate(features, axis=0)

        # 添加2维零特征以匹配模型期望的94维
        padding_features = np.zeros((2, audio_features.shape[1]))
        audio_features = np.concatenate([audio_features, padding_features], axis=0)  # 现在是94维

        # 调整时间维度到目标长度
        current_length = audio_features.shape[1]
        if current_length > target_length:
            # 截断
            audio_features = audio_features[:, :target_length]
        elif current_length < target_length:
            # 填充
            pad_length = target_length - current_length
            padding = np.zeros((audio_features.shape[0], pad_length))
            audio_features = np.concatenate([audio_features, padding], axis=1)
        
        return torch.tensor(audio_features, dtype=torch.float32)
        
    except Exception as e:
        print(f"提取音频特征失败 {audio_path}: {e}")
        # 返回94维零特征
        return torch.zeros(94, target_length, dtype=torch.float32)

def preprocess_all_audio_simple(data_dir: str):
    """预处理所有音频文件 - 简化版本"""
    data_dir = Path(data_dir)
    
    print("🎵 开始预提取音频特征（简化版本）...")
    print(f"📂 数据目录: {data_dir}")
    
    # 扫描所有音频文件
    audio_files = list(data_dir.rglob("*.mp3"))
    print(f"🔍 找到 {len(audio_files)} 个音频文件")
    
    features_cache = {}
    success_count = 0
    
    for audio_file in tqdm(audio_files, desc="提取音频特征"):
        try:
            # 提取特征
            features = extract_simple_audio_features(str(audio_file))
            
            # 保存到缓存 - 使用相对路径作为key
            relative_path = str(audio_file.relative_to(data_dir))
            features_cache[relative_path] = features
            success_count += 1
            
        except Exception as e:
            print(f"❌ 提取失败: {audio_file.name} - {e}")
            continue
    
    # 保存特征缓存
    cache_file = data_dir / "audio_features_cache.pkl"
    with open(cache_file, 'wb') as f:
        pickle.dump(features_cache, f)
    
    print(f"✅ 特征提取完成！")
    print(f"💾 缓存文件: {cache_file}")
    print(f"📊 成功提取: {success_count} / {len(audio_files)} 个文件")
    print(f"💽 缓存大小: {cache_file.stat().st_size / 1024 / 1024:.1f} MB")
    print(f"🎯 特征维度: 94 (MFCC:13 + Mel:64 + ZCR:1 + RMS:1 + Centroid:1 + Chroma:12 + Padding:2)")

def check_librosa_version():
    """检查librosa版本并给出建议"""
    import librosa
    print(f"📦 Librosa版本: {librosa.__version__}")
    
    # 测试关键功能
    try:
        y = np.random.randn(22050)  # 1秒随机音频
        mfcc = librosa.feature.mfcc(y=y, sr=22050, n_mfcc=13)
        print("✅ MFCC功能正常")
        
        mel = librosa.feature.melspectrogram(y=y, sr=22050, n_mels=64)
        print("✅ Mel频谱功能正常")
        
        try:
            chroma = librosa.feature.chroma_stft(y=y, sr=22050)
            print("✅ 色度特征功能正常")
        except:
            print("⚠️  色度特征可能有问题，将使用备用方案")
            
    except Exception as e:
        print(f"❌ Librosa功能测试失败: {e}")
        print("💡 建议升级librosa: pip install librosa>=0.8.0")

if __name__ == "__main__":
    import argparse
    
    # 检查librosa版本
    check_librosa_version()
    print()
    
    parser = argparse.ArgumentParser(description="简化的音频特征预提取")
    parser.add_argument("--data-dir", required=True, help="数据目录")
    
    args = parser.parse_args()
    preprocess_all_audio_simple(args.data_dir)
