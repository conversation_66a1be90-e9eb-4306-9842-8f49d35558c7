"""
节奏大师格式转换器

支持节奏大师音游的.imd格式导入导出，与现有C#项目兼容
"""

import xml.etree.ElementTree as ET
from xml.dom import minidom
from typing import Dict, List, Optional, Any
from pathlib import Path
import logging

from .base_converter import BaseConverter, register_converter, ConversionError
from ..chart_generation.chart_data import ChartData, ChartMetadata, NoteInfo

logger = logging.getLogger(__name__)


@register_converter
class RhythmMasterConverter(BaseConverter):
    """节奏大师格式转换器"""
    
    def __init__(self):
        super().__init__("rhythm_master")
        self.supported_extensions = ['.imd', '.xml']
    
    def export(self, chart_data: ChartData, output_path: str, **kwargs) -> bool:
        """
        导出为节奏大师格式
        
        Args:
            chart_data: 谱面数据
            output_path: 输出文件路径
            **kwargs: 额外参数
                - beat_precision: 节拍精度 (默认64)
                - encoding: XML编码 (默认utf-8)
                
        Returns:
            bool: 是否成功
        """
        try:
            # 验证数据
            errors = self._validate_chart_data(chart_data)
            if errors:
                raise ConversionError(f"谱面数据验证失败: {'; '.join(errors)}", self.format_name)
            
            # 获取参数
            beat_precision = kwargs.get('beat_precision', 64)
            encoding = kwargs.get('encoding', 'utf-8')
            
            # 构建XML结构
            xml_data = self._build_rhythm_master_xml(chart_data, beat_precision)
            
            # 保存文件
            output_path = Path(output_path)
            if output_path.suffix.lower() not in ['.imd', '.xml']:
                output_path = output_path.with_suffix('.imd')
            
            # 格式化XML
            rough_string = ET.tostring(xml_data, encoding='unicode')
            reparsed = minidom.parseString(rough_string)
            pretty_xml = reparsed.toprettyxml(indent="  ", encoding=encoding)
            
            with open(output_path, 'wb') as f:
                f.write(pretty_xml)
            
            self._log_export_info(chart_data, str(output_path))
            return True
            
        except Exception as e:
            logger.error(f"导出节奏大师格式失败: {e}")
            return False
    
    def import_chart(self, file_path: str, **kwargs) -> Optional[ChartData]:
        """
        从节奏大师格式文件导入谱面
        
        Args:
            file_path: 文件路径
            **kwargs: 额外参数
            
        Returns:
            Optional[ChartData]: 导入的谱面数据
        """
        try:
            if not self.validate_file_extension(file_path):
                raise ConversionError(f"不支持的文件扩展名", self.format_name, file_path)
            
            # 解析XML文件
            tree = ET.parse(file_path)
            root = tree.getroot()
            
            # 解析数据
            chart_data = self._parse_rhythm_master_xml(root)
            
            self._log_import_info(chart_data, file_path)
            return chart_data
            
        except Exception as e:
            logger.error(f"导入节奏大师格式失败: {e}")
            return None
    
    def _build_rhythm_master_xml(self, chart_data: ChartData, beat_precision: int) -> ET.Element:
        """
        构建节奏大师XML结构
        
        Args:
            chart_data: 谱面数据
            beat_precision: 节拍精度
            
        Returns:
            ET.Element: XML根元素
        """
        # 创建根元素
        root = ET.Element("LevelInfo")
        
        # 基本信息
        root.set("LevelInfoId", "1")
        root.set("BPM", str(chart_data.metadata.bpm))
        root.set("BeatPerBar", "4")  # 默认4/4拍
        root.set("BeatLen", str(beat_precision))
        root.set("EnterTimeAdjust", "0")
        root.set("NotePreShow", "1")
        root.set("LevelTime", str(int(chart_data.metadata.duration * 1000)))  # 毫秒
        root.set("BarAmount", str(self._calculate_bar_amount(chart_data)))
        root.set("BeginBarLen", "4")
        root.set("IsFourTrack", "true" if chart_data.metadata.track_count == 4 else "false")
        root.set("TrackCount", str(chart_data.metadata.track_count))
        root.set("LevelPreTime", "2000")
        root.set("Star", str(chart_data.metadata.difficulty))
        root.set("SongName", chart_data.metadata.title)
        root.set("Artist", chart_data.metadata.artist)
        
        # 音符数据
        notes_element = ET.SubElement(root, "IdolNoteInfo")
        
        for note in chart_data.get_all_notes():
            note_element = ET.SubElement(notes_element, "IdolNote")
            
            # 计算小节和位置
            bar, pos = self._time_to_bar_pos(note.time, chart_data.metadata.bpm, beat_precision)
            
            note_element.set("IdolNoteInfoID", str(id(note)))
            note_element.set("LevelInfoId", "1")
            note_element.set("Bar", str(bar))
            note_element.set("Pos", str(pos))
            note_element.set("FromTrack", str(note.track))
            note_element.set("TargetTrack", str(note.track))
            
            # 处理音符类型（支持字符串和数字）
            if note.note_type == "tap" or note.note_type == 1:
                # 短音符
                note_element.set("NoteType", "Click")
                note_element.set("EndTrack", "")
                note_element.set("EndBar", "")
                note_element.set("EndPos", "")
            elif note.note_type == "hold" or note.note_type == 2:
                # 长音符
                note_element.set("NoteType", "Hold")
                end_bar, end_pos = self._time_to_bar_pos(
                    note.time + note.duration,
                    chart_data.metadata.bpm,
                    beat_precision
                )
                note_element.set("EndTrack", str(note.track))
                note_element.set("EndBar", str(end_bar))
                note_element.set("EndPos", str(end_pos))
            else:
                # 默认为短音符
                note_element.set("NoteType", "Click")
                note_element.set("EndTrack", "")
                note_element.set("EndBar", "")
                note_element.set("EndPos", "")
            
            note_element.set("CombineNoteNum", "0")
        
        return root
    
    def _parse_rhythm_master_xml(self, root: ET.Element) -> ChartData:
        """
        解析节奏大师XML数据
        
        Args:
            root: XML根元素
            
        Returns:
            ChartData: 解析后的谱面数据
        """
        # 解析元数据
        metadata = ChartMetadata(
            title=root.get("SongName", "Unknown"),
            artist=root.get("Artist", "Unknown"),
            creator="Unknown",
            difficulty=int(root.get("Star", "5")),
            bpm=float(root.get("BPM", "120")),
            duration=float(root.get("LevelTime", "0")) / 1000.0,  # 转换为秒
            track_count=int(root.get("TrackCount", "4"))
        )
        
        chart_data = ChartData(metadata)
        
        # 解析音符
        beat_len = int(root.get("BeatLen", "64"))
        notes_element = root.find("IdolNoteInfo")
        
        if notes_element is not None:
            for note_element in notes_element.findall("IdolNote"):
                try:
                    # 解析基本信息
                    bar = int(note_element.get("Bar", "0"))
                    pos = float(note_element.get("Pos", "0"))
                    track = int(note_element.get("FromTrack", "0"))
                    note_type_str = note_element.get("NoteType", "Click")
                    
                    # 计算时间
                    time = self._bar_pos_to_time(bar, pos, metadata.bpm, beat_len)
                    
                    # 确定音符类型
                    if note_type_str.lower() == "hold":
                        note_type = "hold"  # 长音符
                        end_bar = int(note_element.get("EndBar", str(bar)))
                        end_pos = float(note_element.get("EndPos", str(pos)))
                        end_time = self._bar_pos_to_time(end_bar, end_pos, metadata.bpm, beat_len)
                        duration = max(0.1, end_time - time)
                    else:
                        note_type = "tap"  # 短音符
                        duration = 0.0
                    
                    note = NoteInfo(
                        time=time,
                        track=track,
                        note_type=note_type,
                        duration=duration
                    )
                    
                    chart_data.add_note(note)
                    
                except Exception as e:
                    logger.warning(f"解析音符失败: {e}, 元素: {ET.tostring(note_element, encoding='unicode')}")
                    continue
        
        return chart_data
    
    def _time_to_bar_pos(self, time: float, bpm: float, beat_precision: int) -> tuple:
        """
        将时间转换为小节和位置
        
        Args:
            time: 时间（秒）
            bpm: BPM
            beat_precision: 节拍精度
            
        Returns:
            tuple: (小节号, 位置)
        """
        # 计算总拍数
        beats_per_second = bpm / 60.0
        total_beats = time * beats_per_second
        
        # 计算小节（每小节4拍）
        bar = int(total_beats // 4) + 1  # 小节从1开始
        
        # 计算小节内位置
        beat_in_bar = total_beats % 4
        pos = beat_in_bar * (beat_precision / 4)
        
        return bar, pos
    
    def _bar_pos_to_time(self, bar: int, pos: float, bpm: float, beat_precision: int) -> float:
        """
        将小节和位置转换为时间
        
        Args:
            bar: 小节号
            pos: 位置
            bpm: BPM
            beat_precision: 节拍精度
            
        Returns:
            float: 时间（秒）
        """
        # 计算总拍数
        total_beats = (bar - 1) * 4 + (pos * 4 / beat_precision)
        
        # 转换为时间
        beats_per_second = bpm / 60.0
        time = total_beats / beats_per_second
        
        return time
    
    def _calculate_bar_amount(self, chart_data: ChartData) -> int:
        """
        计算小节数量
        
        Args:
            chart_data: 谱面数据
            
        Returns:
            int: 小节数量
        """
        if chart_data.metadata.duration <= 0:
            return 1
        
        # 根据时长和BPM计算小节数
        beats_per_second = chart_data.metadata.bpm / 60.0
        total_beats = chart_data.metadata.duration * beats_per_second
        bar_amount = int(total_beats // 4) + 1
        
        return max(1, bar_amount)
    
    def get_format_info(self) -> Dict[str, Any]:
        """获取节奏大师格式信息"""
        info = super().get_format_info()
        info.update({
            'description': '节奏大师音游格式 (.imd)',
            'features': [
                '支持4轨道模式',
                '支持点击和长按音符',
                '与C#项目完全兼容',
                '支持BPM和难度设置'
            ],
            'limitations': [
                '不支持滑动音符',
                '轨道数量限制为4个',
                '不支持复杂的音符组合'
            ],
            'compatibility': {
                'csharp_project': True,
                'xml_format': True,
                'encoding': 'utf-8'
            }
        })
        return info
