# 🎵 AI谱面生成器训练指南

## 📋 新增功能：自定义目录参数

现在训练脚本支持灵活指定MP3音频文件和IMD谱面文件的目录位置！

## 🚀 使用方法

### 方法1：传统目录结构 (推荐)

```bash
python train_gan.py --data-dir "data/processed" --epochs 30 --batch-size 8 --device cuda
```

**目录结构**：
```
data/processed/
├── audio/          # MP3音频文件
│   ├── 歌曲1.mp3
│   └── 歌曲2.mp3
└── charts/         # IMD/JSON谱面文件
    ├── 歌曲1_4k.imd
    └── 歌曲2_5k.json
```

### 方法2：分别指定目录 ⭐ **新功能**

```bash
python train_gan.py --audio-dir "mp3_files" --charts-dir "imd_files" --epochs 30 --batch-size 8 --device cuda
```

**目录结构**：
```
项目根目录/
├── mp3_files/      # 你的MP3文件目录
│   ├── 歌曲1.mp3
│   └── 歌曲2.mp3
├── imd_files/      # 你的IMD文件目录
│   ├── 歌曲1.imd
│   └── 歌曲2.imd
└── train_gan.py
```

### 方法3：使用绝对路径

```bash
python train_gan.py \
    --audio-dir "/path/to/your/music/mp3" \
    --charts-dir "/path/to/your/charts/imd" \
    --epochs 50 \
    --batch-size 16 \
    --device cuda
```

### 方法4：Google Colab环境

```bash
python train_gan.py \
    --audio-dir "uploaded_audio" \
    --charts-dir "uploaded_charts" \
    --epochs 20 \
    --batch-size 8 \
    --device cuda \
    --save-interval 5
```

## 📊 完整参数列表

### 数据参数
- `--data-dir`: 传统数据目录（包含audio和charts子目录）
- `--audio-dir`: MP3音频文件目录 ⭐ **新增**
- `--charts-dir`: IMD/JSON谱面文件目录 ⭐ **新增**
- `--track-count`: 轨道数量 (默认: 4)
- `--max-length`: 最大序列长度 (默认: 2000)

### 训练参数
- `--epochs`: 训练轮数 (默认: 100)
- `--batch-size`: 批次大小 (默认: 16)
- `--lr-g`: 生成器学习率 (默认: 0.0002)
- `--lr-d`: 判别器学习率 (默认: 0.0002)
- `--validation-split`: 验证集比例 (默认: 0.2)

### 设备参数
- `--device`: 计算设备 (auto/cpu/cuda, 默认: auto)
- `--num-workers`: 数据加载线程数 (默认: 4)

### 保存参数
- `--save-dir`: 模型保存目录 (默认: models)
- `--save-interval`: 保存间隔 (默认: 10)
- `--resume`: 恢复训练的模型路径

## 🎯 文件命名规则

### 音频文件
支持格式：`.mp3`, `.wav`, `.flac`, `.m4a`

### 谱面文件
支持格式：`.imd`, `.json`, `.xml`, `.mc`, `.osu`

### 文件匹配规则
音频和谱面文件通过文件名匹配：

**示例1：完全匹配**
```
音频: 节奏大师-贝多芬病毒.mp3
谱面: 节奏大师-贝多芬病毒.imd
```

**示例2：带轨道数后缀**
```
音频: 节奏大师-贝多芬病毒.mp3
谱面: 节奏大师-贝多芬病毒_4k.imd
```

**示例3：带难度后缀**
```
音频: 节奏大师-贝多芬病毒.mp3
谱面: 节奏大师-贝多芬病毒_4k_hd.imd
```

## 🔍 使用示例脚本

### 检查目录和文件
```bash
python train_with_custom_dirs.py check mp3_files imd_files
```

### 查看所有配置示例
```bash
python train_with_custom_dirs.py
```

### 自动检测并运行训练
```bash
python train_with_custom_dirs.py run
```

## 💡 最佳实践

### 1. 目录组织
```
推荐结构：
my_music_project/
├── audio/              # 所有MP3文件
├── charts/             # 所有谱面文件
├── models/             # 训练输出的模型
└── logs/               # 训练日志
```

### 2. 文件准备
- 确保音频和谱面文件名匹配
- 使用一致的命名规则
- 检查文件完整性

### 3. 训练配置
```bash
# 开发测试（快速验证）
python train_gan.py --audio-dir audio --charts-dir charts --epochs 5 --batch-size 2

# 正式训练（GPU环境）
python train_gan.py --audio-dir audio --charts-dir charts --epochs 100 --batch-size 16 --device cuda

# Colab训练（平衡性能和时间）
python train_gan.py --audio-dir audio --charts-dir charts --epochs 30 --batch-size 8 --device cuda
```

## 🚨 常见问题

### Q: 如何知道我的文件是否正确匹配？
A: 运行训练前会显示加载的数据对数量，如果为0说明没有匹配的文件。

### Q: 支持哪些音频格式？
A: 支持 MP3, WAV, FLAC, M4A 格式。

### Q: 支持哪些谱面格式？
A: 支持 IMD, JSON, XML, MC, OSU 格式。

### Q: 可以混合使用不同格式吗？
A: 可以！同一个目录中可以有不同格式的文件。

### Q: 如何在Colab中使用？
A: 上传文件到指定目录，然后使用 `--audio-dir` 和 `--charts-dir` 参数指定路径。

## 🎉 开始训练

选择适合你的方法，准备好数据，开始训练你的AI谱面生成器吧！

```bash
# 最简单的开始方式
python train_gan.py --audio-dir your_mp3_folder --charts-dir your_imd_folder --epochs 10
```
