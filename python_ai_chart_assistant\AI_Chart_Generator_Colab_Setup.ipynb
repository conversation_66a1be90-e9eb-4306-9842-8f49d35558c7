# 挂载Google Drive
from google.colab import drive
drive.mount('/content/drive')

# 检查GPU
import torch
device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
print(f"🚀 使用设备: {device}")
if torch.cuda.is_available():
    print(f"GPU: {torch.cuda.get_device_name(0)}")
    print(f"显存: {torch.cuda.get_device_properties(0).total_memory / 1024**3:.1f} GB")

# 安装依赖包
!pip install librosa tqdm pyyaml pretty_midi mido -q
print("✅ 依赖包安装完成")

import os
import zipfile

# 创建项目目录
project_dir = "/content/drive/MyDrive/python_ai_chart_assistant"
os.makedirs(project_dir, exist_ok=True)
print(f"📁 项目目录: {project_dir}")

# 检查压缩包
archive_path = "/content/drive/MyDrive/python_ai_chart_assistant.zip"
if os.path.exists(archive_path):
    print("📦 找到压缩包，开始解压...")
    
    # 解压项目文件
    with zipfile.ZipFile(archive_path, 'r') as zipf:
        zipf.extractall(project_dir)
    print("✅ 解压完成!")
    
    # 列出主要文件
    print("📋 项目文件:")
    for item in sorted(os.listdir(project_dir)):
        item_path = os.path.join(project_dir, item)
        if os.path.isdir(item_path):
            print(f"  📁 {item}/")
        elif item.endswith('.py'):
            print(f"  🐍 {item}")
        else:
            print(f"  📄 {item}")
else:
    print(f"❌ 未找到压缩包: {archive_path}")
    print("请先上传 python_ai_chart_assistant.zip 到Google Drive根目录")

# 设置工作目录
os.chdir(project_dir)
print(f"📂 当前工作目录: {os.getcwd()}")

# 测试模块导入
import sys
if project_dir not in sys.path:
    sys.path.append(project_dir)

try:
    from src.models.audio_chart_gan import AudioChartGAN
    from src.training.audio_chart_dataset import AudioChartDataset
    print("✅ 核心模块导入成功!")
except Exception as e:
    print(f"❌ 模块导入失败: {e}")
    print("请检查项目文件是否完整")

# 创建必要目录
os.makedirs("models", exist_ok=True)
os.makedirs("data", exist_ok=True)
print("📁 目录结构创建完成")

import torch
import pickle
import numpy as np

# 创建演示数据目录
demo_data_dir = os.path.join(project_dir, "data", "training_demo")
os.makedirs(demo_data_dir, exist_ok=True)

print("📊 创建演示数据...")

# 创建示例音频特征缓存
dummy_features = {}
for i in range(50):  # 50个示例文件
    # 94维特征，2000时间步
    features = torch.randn(94, 2000)
    dummy_features[f"demo_song_{i:02d}.mp3"] = features

# 保存特征缓存
cache_file = os.path.join(demo_data_dir, "audio_features_cache.pkl")
with open(cache_file, 'wb') as f:
    pickle.dump(dummy_features, f)

print(f"✅ 演示数据创建完成: {demo_data_dir}")
print(f"📊 包含 {len(dummy_features)} 个示例音频特征")
print(f"💾 缓存文件: {cache_file}")

# 快速训练测试 (2轮，小批次)
print("🚀 开始快速训练测试...")
print("⏱️  预计用时: 10-15分钟")

!python train_optimized.py \
  --data-dir "data/training_demo" \
  --save-dir "models/colab_quick_test" \
  --epochs 2 \
  --batch-size 4 \
  --lr-d 0.0001 \
  --lr-g 0.0002

print("🎉 快速训练测试完成!")

# 完整训练 (更多轮次，更大批次)
print("🎯 开始完整训练...")
print("⏱️  预计用时: 2-3小时")
print("💡 建议在Colab Pro上运行以获得更好的GPU")

!python train_optimized.py \
  --data-dir "data/training_demo" \
  --save-dir "models/colab_full_training" \
  --epochs 20 \
  --batch-size 8 \
  --lr-d 0.0001 \
  --lr-g 0.0002

print("🎉 完整训练完成!")

# 检查保存的模型
models_dir = os.path.join(project_dir, "models")
print("📋 训练结果:")

for root, dirs, files in os.walk(models_dir):
    level = root.replace(models_dir, '').count(os.sep)
    indent = '  ' * level
    print(f"{indent}📁 {os.path.basename(root)}/")
    subindent = '  ' * (level + 1)
    for file in files:
        if file.endswith('.pth'):
            file_path = os.path.join(root, file)
            file_size = os.path.getsize(file_path) / 1024 / 1024  # MB
            print(f"{subindent}🤖 {file} ({file_size:.1f} MB)")
        else:
            print(f"{subindent}📄 {file}")

print("\n✅ 检查完成! 模型文件已保存到Google Drive")