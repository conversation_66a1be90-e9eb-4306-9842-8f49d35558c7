{"cells": [{"cell_type": "markdown", "metadata": {"id": "header"}, "source": ["# 🎵 AI谱面生成器 - Google Colab版\n", "\n", "这个笔记本将帮助你在Google Colab上设置和运行AI谱面生成项目。\n", "\n", "## 📋 使用步骤\n", "1. **上传项目文件** - 将压缩包上传到Google Drive\n", "2. **设置环境** - 安装依赖和挂载Drive\n", "3. **解压项目** - 解压并设置工作目录\n", "4. **开始训练** - 运行AI模型训练\n", "\n", "---"]}, {"cell_type": "markdown", "metadata": {"id": "step1"}, "source": ["## 🚀 步骤1: 挂载Google Drive并安装依赖"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "setup_env"}, "outputs": [], "source": ["# 挂载Google Drive\n", "from google.colab import drive\n", "drive.mount('/content/drive')\n", "\n", "# 检查GPU\n", "import torch\n", "device = torch.device(\"cuda\" if torch.cuda.is_available() else \"cpu\")\n", "print(f\"🚀 使用设备: {device}\")\n", "if torch.cuda.is_available():\n", "    print(f\"GPU: {torch.cuda.get_device_name(0)}\")\n", "    print(f\"显存: {torch.cuda.get_device_properties(0).total_memory / 1024**3:.1f} GB\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "install_deps"}, "outputs": [], "source": ["# 安装依赖包\n", "!pip install librosa tqdm pyyaml pretty_midi mido -q\n", "print(\"✅ 依赖包安装完成\")"]}, {"cell_type": "markdown", "metadata": {"id": "step2"}, "source": ["## 📦 步骤2: 上传项目压缩包\n", "\n", "**请先完成以下操作:**\n", "1. 在本地运行: `python upload_to_colab.py --create-archive`\n", "2. 将生成的 `python_ai_chart_assistant.zip` 上传到Google Drive根目录\n", "3. 然后运行下面的代码"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "extract_project"}, "outputs": [], "source": ["import os\n", "import zipfile\n", "\n", "# 创建项目目录\n", "project_dir = \"/content/drive/MyDrive/python_ai_chart_assistant\"\n", "os.makedirs(project_dir, exist_ok=True)\n", "print(f\"📁 项目目录: {project_dir}\")\n", "\n", "# 检查压缩包\n", "archive_path = \"/content/drive/MyDrive/python_ai_chart_assistant.zip\"\n", "if os.path.exists(archive_path):\n", "    print(\"📦 找到压缩包，开始解压...\")\n", "    \n", "    # 解压项目文件\n", "    with zipfile.ZipFile(archive_path, 'r') as zipf:\n", "        zipf.extractall(project_dir)\n", "    print(\"✅ 解压完成!\")\n", "    \n", "    # 列出主要文件\n", "    print(\"📋 项目文件:\")\n", "    for item in sorted(os.listdir(project_dir)):\n", "        item_path = os.path.join(project_dir, item)\n", "        if os.path.isdir(item_path):\n", "            print(f\"  📁 {item}/\")\n", "        elif item.endswith('.py'):\n", "            print(f\"  🐍 {item}\")\n", "        else:\n", "            print(f\"  📄 {item}\")\n", "else:\n", "    print(f\"❌ 未找到压缩包: {archive_path}\")\n", "    print(\"请先上传 python_ai_chart_assistant.zip 到Google Drive根目录\")"]}, {"cell_type": "markdown", "metadata": {"id": "step3"}, "source": ["## 🧪 步骤3: 测试项目设置"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "test_setup"}, "outputs": [], "source": ["# 设置工作目录\n", "os.chdir(project_dir)\n", "print(f\"📂 当前工作目录: {os.getcwd()}\")\n", "\n", "# 测试模块导入\n", "import sys\n", "if project_dir not in sys.path:\n", "    sys.path.append(project_dir)\n", "\n", "try:\n", "    from src.models.audio_chart_gan import AudioChartGAN\n", "    from src.training.audio_chart_dataset import AudioChartDataset\n", "    print(\"✅ 核心模块导入成功!\")\n", "except Exception as e:\n", "    print(f\"❌ 模块导入失败: {e}\")\n", "    print(\"请检查项目文件是否完整\")\n", "\n", "# 创建必要目录\n", "os.makedirs(\"models\", exist_ok=True)\n", "os.makedirs(\"data\", exist_ok=True)\n", "print(\"📁 目录结构创建完成\")"]}, {"cell_type": "markdown", "metadata": {"id": "step4"}, "source": ["## 📊 步骤4: 创建演示数据"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "create_demo_data"}, "outputs": [], "source": ["import torch\n", "import pickle\n", "import numpy as np\n", "\n", "# 创建演示数据目录\n", "demo_data_dir = os.path.join(project_dir, \"data\", \"training_demo\")\n", "os.makedirs(demo_data_dir, exist_ok=True)\n", "\n", "print(\"📊 创建演示数据...\")\n", "\n", "# 创建示例音频特征缓存\n", "dummy_features = {}\n", "for i in range(50):  # 50个示例文件\n", "    # 94维特征，2000时间步\n", "    features = torch.randn(94, 2000)\n", "    dummy_features[f\"demo_song_{i:02d}.mp3\"] = features\n", "\n", "# 保存特征缓存\n", "cache_file = os.path.join(demo_data_dir, \"audio_features_cache.pkl\")\n", "with open(cache_file, 'wb') as f:\n", "    pickle.dump(dummy_features, f)\n", "\n", "print(f\"✅ 演示数据创建完成: {demo_data_dir}\")\n", "print(f\"📊 包含 {len(dummy_features)} 个示例音频特征\")\n", "print(f\"💾 缓存文件: {cache_file}\")"]}, {"cell_type": "markdown", "metadata": {"id": "step5"}, "source": ["## 🚀 步骤5: 开始训练测试"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "quick_training_test"}, "outputs": [], "source": ["# 快速训练测试 (2轮，小批次)\n", "print(\"🚀 开始快速训练测试...\")\n", "print(\"⏱️  预计用时: 10-15分钟\")\n", "\n", "!python train_optimized.py \\\n", "  --data-dir \"data/training_demo\" \\\n", "  --save-dir \"models/colab_quick_test\" \\\n", "  --epochs 2 \\\n", "  --batch-size 4 \\\n", "  --lr-d 0.0001 \\\n", "  --lr-g 0.0002\n", "\n", "print(\"🎉 快速训练测试完成!\")"]}, {"cell_type": "markdown", "metadata": {"id": "step6"}, "source": ["## 🎯 步骤6: 完整训练 (可选)"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "full_training"}, "outputs": [], "source": ["# 完整训练 (更多轮次，更大批次)\n", "print(\"🎯 开始完整训练...\")\n", "print(\"⏱️  预计用时: 2-3小时\")\n", "print(\"💡 建议在Colab Pro上运行以获得更好的GPU\")\n", "\n", "!python train_optimized.py \\\n", "  --data-dir \"data/training_demo\" \\\n", "  --save-dir \"models/colab_full_training\" \\\n", "  --epochs 20 \\\n", "  --batch-size 8 \\\n", "  --lr-d 0.0001 \\\n", "  --lr-g 0.0002\n", "\n", "print(\"🎉 完整训练完成!\")"]}, {"cell_type": "markdown", "metadata": {"id": "step7"}, "source": ["## 📋 步骤7: 检查训练结果"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "check_results"}, "outputs": [], "source": ["# 检查保存的模型\n", "models_dir = os.path.join(project_dir, \"models\")\n", "print(\"📋 训练结果:\")\n", "\n", "for root, dirs, files in os.walk(models_dir):\n", "    level = root.replace(models_dir, '').count(os.sep)\n", "    indent = '  ' * level\n", "    print(f\"{indent}📁 {os.path.basename(root)}/\")\n", "    subindent = '  ' * (level + 1)\n", "    for file in files:\n", "        if file.endswith('.pth'):\n", "            file_path = os.path.join(root, file)\n", "            file_size = os.path.getsize(file_path) / 1024 / 1024  # MB\n", "            print(f\"{subindent}🤖 {file} ({file_size:.1f} MB)\")\n", "        else:\n", "            print(f\"{subindent}📄 {file}\")\n", "\n", "print(\"\\n✅ 检查完成! 模型文件已保存到Google Drive\")"]}, {"cell_type": "markdown", "metadata": {"id": "tips"}, "source": ["## 💡 使用提示\n", "\n", "### 🚀 性能优化\n", "- **Colab Pro**: 获得更好的GPU (V100/A100) 和更长的运行时间\n", "- **批次大小**: 根据GPU显存调整 `--batch-size` (4-16)\n", "- **学习率**: 可以尝试不同的学习率组合\n", "\n", "### 📊 监控训练\n", "- 观察损失值变化趋势\n", "- 判别器损失应该稳定在0.5-1.5之间\n", "- 生成器损失应该逐渐下降\n", "\n", "### 💾 保存和恢复\n", "- 模型自动保存到Google Drive\n", "- 可以随时停止和恢复训练\n", "- 最佳模型会自动保存为 `optimized_gan_best.pth`\n", "\n", "### 🎵 使用真实数据\n", "如果你有真实的音频和谱面数据:\n", "1. 将数据上传到 `data/your_dataset/` 目录\n", "2. 运行 `python preprocess_audio_simple.py --data-dir data/your_dataset`\n", "3. 使用真实数据路径进行训练\n", "\n", "---\n", "\n", "🎉 **恭喜! 你已经成功在Google Colab上运行AI谱面生成器!**"]}], "metadata": {"accelerator": "GPU", "colab": {"gpuType": "T4", "provenance": []}, "kernelspec": {"display_name": "Python 3", "name": "python3"}, "language_info": {"name": "python"}}, "nbformat": 4, "nbformat_minor": 0}