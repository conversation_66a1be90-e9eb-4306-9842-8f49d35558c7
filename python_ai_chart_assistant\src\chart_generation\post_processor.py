"""
谱面后处理器

对生成的谱面进行优化和修正
"""

import numpy as np
from typing import Dict, List, Optional, Tuple
import logging
from .chart_data import ChartData, NoteInfo

logger = logging.getLogger(__name__)


class ChartPostProcessor:
    """谱面后处理器"""
    
    def __init__(self):
        self.conflict_resolution_distance = 0.1  # 冲突解决距离（秒）
        self.density_smoothing_window = 2.0      # 密度平滑窗口（秒）
        self.balance_tolerance = 0.3             # 轨道平衡容差
    
    def process(self, chart_data: ChartData, beat_info: Optional[Dict] = None) -> ChartData:
        """
        处理谱面数据
        
        Args:
            chart_data: 原始谱面数据
            beat_info: 节拍信息
            
        Returns:
            ChartData: 处理后的谱面数据
        """
        logger.info("开始后处理谱面")
        
        # 1. 冲突检测和解决
        chart_data = self._resolve_conflicts(chart_data)
        
        # 2. 密度平滑
        chart_data = self._smooth_density(chart_data)
        
        # 3. 轨道平衡
        chart_data = self._balance_tracks(chart_data)
        
        # 4. 长音符优化
        chart_data = self._optimize_long_notes(chart_data)
        
        # 5. 节拍对齐（如果有节拍信息）
        if beat_info:
            chart_data = self._align_to_beats(chart_data, beat_info)
        
        # 6. 最终验证
        errors = chart_data.validate()
        if errors:
            logger.warning(f"后处理后仍有验证错误: {errors}")
        
        logger.info("谱面后处理完成")
        return chart_data
    
    def _resolve_conflicts(self, chart_data: ChartData) -> ChartData:
        """
        解决音符冲突，包括长音符重叠

        Args:
            chart_data: 谱面数据

        Returns:
            ChartData: 解决冲突后的谱面数据
        """
        notes = chart_data.get_all_notes()
        if not notes:
            return chart_data

        # 按时间排序
        notes.sort(key=lambda x: x.time)

        resolved_notes = []
        conflicts_resolved = 0

        # 按轨道分组处理
        tracks = {}
        for note in notes:
            if note.note_type == 0:  # 跳过空音符
                continue
            if note.track not in tracks:
                tracks[note.track] = []
            tracks[note.track].append(note)

        # 对每个轨道单独处理重叠 - 使用更简单的策略
        for track_num, track_notes in tracks.items():
            track_notes.sort(key=lambda x: x.time)

            track_resolved = []

            for note in track_notes:
                # 检查是否与已添加的音符重叠
                has_conflict = False

                for existing_note in track_resolved:
                    # 检查时间重叠
                    if existing_note.note_type == 2:  # 如果已有音符是长音符
                        existing_end = existing_note.time + getattr(existing_note, 'duration', 0)
                        if note.time < existing_end:
                            has_conflict = True
                            conflicts_resolved += 1
                            break

                if not has_conflict:
                    track_resolved.append(note)
                # 如果有冲突，直接跳过这个音符（最简单的策略）

            resolved_notes.extend(track_resolved)

        if conflicts_resolved > 0:
            logger.info(f"解决了 {conflicts_resolved} 个音符重叠冲突")

        # 创建新的谱面数据
        new_chart = ChartData(chart_data.metadata)
        new_chart.add_notes(resolved_notes)

        return new_chart
    
    def _smooth_density(self, chart_data: ChartData) -> ChartData:
        """
        平滑音符密度
        
        Args:
            chart_data: 谱面数据
            
        Returns:
            ChartData: 密度平滑后的谱面数据
        """
        notes = chart_data.get_all_notes()
        if len(notes) < 10:  # 音符太少，不需要平滑
            return chart_data
        
        # 计算时间窗口内的密度
        duration = chart_data.metadata.duration
        window_size = self.density_smoothing_window
        
        time_windows = []
        current_time = 0.0
        
        while current_time < duration:
            window_notes = [
                note for note in notes 
                if current_time <= note.time < current_time + window_size
            ]
            
            time_windows.append({
                'start_time': current_time,
                'end_time': current_time + window_size,
                'notes': window_notes,
                'density': len(window_notes) / window_size
            })
            
            current_time += window_size / 2  # 50%重叠
        
        # 计算平均密度
        avg_density = np.mean([w['density'] for w in time_windows])
        
        # 移除密度过高的音符
        notes_to_remove = []
        removed_count = 0
        
        for window in time_windows:
            if window['density'] > avg_density * 2:  # 密度超过平均值2倍
                # 移除一些音符
                window_notes = window['notes']
                if len(window_notes) > 2:
                    # 按力度排序，移除力度较低的音符
                    window_notes.sort(key=lambda x: x.velocity)
                    remove_count = len(window_notes) - int(avg_density * window_size)
                    remove_count = max(0, min(remove_count, len(window_notes) // 2))
                    
                    for i in range(remove_count):
                        notes_to_remove.append(window_notes[i])
                        removed_count += 1
        
        if removed_count > 0:
            logger.info(f"为平滑密度移除了 {removed_count} 个音符")
            
            # 创建新的音符列表
            filtered_notes = [
                note for note in notes 
                if note not in notes_to_remove
            ]
            
            new_chart = ChartData(chart_data.metadata)
            new_chart.add_notes(filtered_notes)
            return new_chart
        
        return chart_data
    
    def _balance_tracks(self, chart_data: ChartData) -> ChartData:
        """
        平衡轨道音符分布
        
        Args:
            chart_data: 谱面数据
            
        Returns:
            ChartData: 轨道平衡后的谱面数据
        """
        notes = chart_data.get_all_notes()
        if not notes:
            return chart_data
        
        # 统计各轨道音符数量
        track_counts = {}
        for note in notes:
            track_counts[note.track] = track_counts.get(note.track, 0) + 1
        
        if not track_counts:
            return chart_data
        
        # 计算平均音符数
        avg_count = sum(track_counts.values()) / len(track_counts)
        
        # 找出需要调整的轨道
        overloaded_tracks = []
        underloaded_tracks = []
        
        for track, count in track_counts.items():
            if count > avg_count * (1 + self.balance_tolerance):
                overloaded_tracks.append(track)
            elif count < avg_count * (1 - self.balance_tolerance):
                underloaded_tracks.append(track)
        
        if not overloaded_tracks or not underloaded_tracks:
            return chart_data
        
        # 重新分配音符
        modified_notes = []
        moved_count = 0
        
        for note in notes:
            if note.track in overloaded_tracks and underloaded_tracks:
                # 随机选择一个负载不足的轨道
                new_track = np.random.choice(underloaded_tracks)
                
                # 检查新轨道在该时间点是否有冲突
                conflict = any(
                    abs(other_note.time - note.time) < self.conflict_resolution_distance
                    and other_note.track == new_track
                    for other_note in modified_notes
                )
                
                if not conflict:
                    new_note = NoteInfo(
                        time=note.time,
                        track=new_track,
                        note_type=note.note_type,
                        duration=note.duration,
                        velocity=note.velocity
                    )
                    modified_notes.append(new_note)
                    moved_count += 1
                    
                    # 更新计数
                    track_counts[note.track] -= 1
                    track_counts[new_track] = track_counts.get(new_track, 0) + 1
                    
                    # 重新评估轨道状态
                    if track_counts[note.track] <= avg_count * (1 + self.balance_tolerance):
                        overloaded_tracks.remove(note.track)
                    if track_counts[new_track] >= avg_count * (1 - self.balance_tolerance):
                        underloaded_tracks.remove(new_track)
                else:
                    modified_notes.append(note)
            else:
                modified_notes.append(note)
        
        if moved_count > 0:
            logger.info(f"为平衡轨道移动了 {moved_count} 个音符")
            
            new_chart = ChartData(chart_data.metadata)
            new_chart.add_notes(modified_notes)
            return new_chart
        
        return chart_data
    
    def _optimize_long_notes(self, chart_data: ChartData) -> ChartData:
        """
        优化长音符
        
        Args:
            chart_data: 谱面数据
            
        Returns:
            ChartData: 优化后的谱面数据
        """
        notes = chart_data.get_all_notes()
        optimized_notes = []
        optimized_count = 0
        
        for note in notes:
            if note.note_type == 2:  # 长音符
                # 检查长音符的合理性
                min_duration = 0.2  # 最小持续时间
                max_duration = 4.0  # 最大持续时间
                
                if note.duration < min_duration:
                    # 转换为短音符
                    new_note = NoteInfo(
                        time=note.time,
                        track=note.track,
                        note_type=1,  # 短音符
                        velocity=note.velocity
                    )
                    optimized_notes.append(new_note)
                    optimized_count += 1
                elif note.duration > max_duration:
                    # 缩短持续时间
                    new_note = NoteInfo(
                        time=note.time,
                        track=note.track,
                        note_type=2,
                        duration=max_duration,
                        velocity=note.velocity
                    )
                    optimized_notes.append(new_note)
                    optimized_count += 1
                else:
                    optimized_notes.append(note)
            else:
                optimized_notes.append(note)
        
        if optimized_count > 0:
            logger.info(f"优化了 {optimized_count} 个长音符")
            
            new_chart = ChartData(chart_data.metadata)
            new_chart.add_notes(optimized_notes)
            return new_chart
        
        return chart_data
    
    def _align_to_beats(self, chart_data: ChartData, beat_info: Dict) -> ChartData:
        """
        将音符对齐到节拍
        
        Args:
            chart_data: 谱面数据
            beat_info: 节拍信息
            
        Returns:
            ChartData: 对齐后的谱面数据
        """
        notes = chart_data.get_all_notes()
        if not notes or 'beat_strength' not in beat_info:
            return chart_data
        
        beat_points = [beat['time'] for beat in beat_info['beat_strength']]
        if not beat_points:
            return chart_data
        
        aligned_notes = []
        aligned_count = 0
        alignment_threshold = 0.2  # 对齐阈值（秒）
        
        for note in notes:
            # 找到最近的节拍点
            distances = [abs(note.time - beat_time) for beat_time in beat_points]
            min_distance = min(distances)
            
            if min_distance <= alignment_threshold:
                # 对齐到最近的节拍点
                closest_beat_idx = distances.index(min_distance)
                aligned_time = beat_points[closest_beat_idx]
                
                aligned_note = NoteInfo(
                    time=aligned_time,
                    track=note.track,
                    note_type=note.note_type,
                    duration=note.duration,
                    velocity=note.velocity
                )
                aligned_notes.append(aligned_note)
                aligned_count += 1
            else:
                aligned_notes.append(note)
        
        if aligned_count > 0:
            logger.info(f"对齐了 {aligned_count} 个音符到节拍点")
            
            new_chart = ChartData(chart_data.metadata)
            new_chart.add_notes(aligned_notes)
            return new_chart
        
        return chart_data
