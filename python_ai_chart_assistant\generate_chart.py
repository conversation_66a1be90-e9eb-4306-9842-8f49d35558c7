#!/usr/bin/env python3
"""
AI音游谱面生成器 - 带参数控制
支持自定义难度、风格等参数
"""

import torch
import sys
import os
import argparse
from pathlib import Path
import logging

# 添加项目路径
sys.path.insert(0, str(Path(__file__).parent))

from src.models.audio_chart_gan import AudioChartGAN
from src.utils.logger_setup import setup_logging
import json
import numpy as np
import random
from datetime import datetime

def generate_chart_with_params(audio_path, output_path, difficulty=7, style="balanced", title=None, artist=None):
    """
    使用指定参数生成谱面
    
    Args:
        audio_path: 音频文件路径
        output_path: 输出文件路径
        difficulty: 难度等级 (1-10)
        style: 生成风格
        title: 歌曲标题
        artist: 艺术家
    """
    
    # 设置日志
    setup_logging()
    logger = logging.getLogger(__name__)
    
    model_path = "models/final_gan_model.pth"
    
    logger.info(f"🎵 开始生成谱面")
    logger.info(f"   音频文件: {audio_path}")
    logger.info(f"   输出文件: {output_path}")
    logger.info(f"   难度等级: {difficulty}")
    logger.info(f"   生成风格: {style}")
    
    # 检查文件
    if not os.path.exists(audio_path):
        logger.error(f"❌ 音频文件不存在: {audio_path}")
        return False
        
    if not os.path.exists(model_path):
        logger.error(f"❌ 模型文件不存在: {model_path}")
        return False
    
    try:
        # 1. 加载AI模型
        logger.info("🤖 加载AI模型...")
        device = 'cuda' if torch.cuda.is_available() else 'cpu'
        
        model = AudioChartGAN(track_count=4, max_length=2000)
        
        # 加载模型权重
        checkpoint = torch.load(model_path, map_location=device)
        if isinstance(checkpoint, dict) and 'model_state_dict' in checkpoint:
            model_state = checkpoint['model_state_dict']
            if 'generator' in model_state:
                model.generator.load_state_dict(model_state['generator'])
                model.discriminator.load_state_dict(model_state['discriminator'])
        
        model.to(device)
        model.eval()
        logger.info("✅ AI模型加载成功")
        
        # 2. 检测音频BPM
        logger.info(f"🎵 检测音频BPM...")
        import librosa
        y, sr = librosa.load(audio_path, sr=22050)
        detected_bpm, _ = librosa.beat.beat_track(y=y, sr=sr)
        # 确保BPM是单个数值
        if hasattr(detected_bpm, '__len__') and len(detected_bpm) > 0:
            bpm_value = float(detected_bpm[0])
        else:
            bpm_value = float(detected_bpm)
        logger.info(f"🎼 检测到BPM: {bpm_value:.1f}")

        # 3. 生成谱面
        logger.info(f"🎵 AI分析音频...")
        chart_array = model.generate_chart(audio_path)
        logger.info(f"📊 生成谱面: {chart_array.shape}")

        # 4. 根据难度调整音符密度
        chart_array = adjust_difficulty(chart_array, difficulty)

        # 5. 平衡轨道分布
        chart_array = balance_track_distribution(chart_array)

        # 6. 转换为JSON格式谱面数据
        time_resolution = 0.125
        duration = len(chart_array) * time_resolution

        # 获取音频文件名作为默认标题
        if not title:
            title = Path(audio_path).stem
        if not artist:
            artist = "AI Assistant"

        # 转换音符
        notes = []
        total_notes = 0

        for time_idx, time_step in enumerate(chart_array):
            time_ms = int(time_idx * time_resolution * 1000)

            for track_idx, note_type in enumerate(time_step):
                if note_type > 0:
                    # 扩展音符类型映射
                    note_type_map = {
                        1: {"type": "tap", "duration": 0},
                        2: {"type": "hold", "duration": 500},
                        3: {"type": "slide", "duration": 0}  # 添加滑动音符
                    }

                    # 如果原始类型超出范围，随机分配类型
                    if note_type not in note_type_map:
                        note_type = random.choice([1, 2, 3])

                    note_info = note_type_map[note_type]
                    note = {
                        "time": time_ms,
                        "track": track_idx + 1,  # 轨道从1开始编号
                        "type": note_info["type"],
                        "duration": note_info["duration"]
                    }

                    # 为滑动音符添加目标轨道
                    if note_info["type"] == "slide":
                        # 随机选择一个不同的轨道作为滑动目标
                        available_tracks = [t for t in range(1, 5) if t != track_idx + 1]
                        if available_tracks:
                            note["target_track"] = random.choice(available_tracks)
                        else:
                            note["target_track"] = track_idx + 1  # 如果没有其他轨道，保持原轨道

                    notes.append(note)
                    total_notes += 1

        # 5. 创建完整的JSON谱面数据
        chart_data = {
            "metadata": {
                "title": title,
                "artist": artist,
                "creator": "AI Chart Generator",
                "difficulty": difficulty,
                "style": style,
                "bpm": bpm_value,  # 使用检测到的真实BPM
                "duration": duration,
                "track_count": 4,
                "total_notes": total_notes,
                "note_density": total_notes / duration if duration > 0 else 0,
                "generated_time": str(datetime.now()),
                "ai_model": "AudioChartGAN",
                "version": "1.0"
            },
            "notes": notes,
            "statistics": {
                "track_distribution": {},
                "note_types": {"tap": 0, "hold": 0, "slide": 0},
                "time_range": {
                    "start": 0,
                    "end": int(duration * 1000)
                }
            }
        }

        # 6. 计算统计信息
        for track in range(1, 5):
            track_notes = len([n for n in notes if n["track"] == track])
            chart_data["statistics"]["track_distribution"][f"track_{track}"] = {
                "count": track_notes,
                "percentage": (track_notes / total_notes * 100) if total_notes > 0 else 0
            }

        for note in notes:
            chart_data["statistics"]["note_types"][note["type"]] += 1

        logger.info(f"📝 生成了 {total_notes} 个音符")

        # 7. 保存为JSON文件
        logger.info(f"💾 保存JSON谱面文件...")
        try:
            with open(output_path, 'w', encoding='utf-8') as f:
                json.dump(chart_data, f, indent=2, ensure_ascii=False)

            logger.info("🎉 谱面生成成功！")
            logger.info(f"📁 输出文件: {output_path}")
            logger.info(f"📊 谱面统计:")
            logger.info(f"   总时长: {duration:.1f} 秒")
            logger.info(f"   BPM: {bpm_value:.1f}")
            logger.info(f"   音符数量: {total_notes}")
            logger.info(f"   难度等级: {difficulty}")
            logger.info(f"   音符密度: {total_notes/duration:.1f} 音符/秒")

            # 显示轨道分布
            logger.info(f"   轨道分布:")
            for track in range(1, 5):
                track_notes = len([n for n in notes if n["track"] == track])
                percentage = (track_notes / total_notes * 100) if total_notes > 0 else 0
                logger.info(f"     轨道{track}: {track_notes} 个音符 ({percentage:.1f}%)")

            return True

        except Exception as e:
            logger.error(f"❌ 保存JSON文件失败: {e}")
            return False
            
    except Exception as e:
        logger.error(f"❌ 生成失败: {e}")
        import traceback
        logger.error(traceback.format_exc())
        return False

def adjust_difficulty(chart_array, difficulty):
    """
    根据难度调整谱面，并增加音符类型多样性

    Args:
        chart_array: 原始谱面数组
        difficulty: 目标难度 (1-10)

    Returns:
        调整后的谱面数组
    """
    import numpy as np
    import random

    # 难度系数映射
    difficulty_factors = {
        1: 0.2,   # 极简单：只保留20%的音符
        2: 0.3,   # 很简单：保留30%的音符
        3: 0.4,   # 简单：保留40%的音符
        4: 0.5,   # 较简单：保留50%的音符
        5: 0.6,   # 中等：保留60%的音符
        6: 0.7,   # 较难：保留70%的音符
        7: 0.8,   # 难：保留80%的音符
        8: 0.9,   # 很难：保留90%的音符
        9: 1.0,   # 极难：保留所有音符
        10: 1.2   # 地狱：增加20%音符密度
    }

    # 音符类型概率分布（根据难度调整）
    note_type_probabilities = {
        1: {"tap": 0.9, "hold": 0.1, "slide": 0.0},    # 简单：主要是tap
        2: {"tap": 0.8, "hold": 0.15, "slide": 0.05},
        3: {"tap": 0.7, "hold": 0.2, "slide": 0.1},
        4: {"tap": 0.65, "hold": 0.25, "slide": 0.1},
        5: {"tap": 0.6, "hold": 0.25, "slide": 0.15},   # 中等：平衡分布
        6: {"tap": 0.55, "hold": 0.3, "slide": 0.15},
        7: {"tap": 0.5, "hold": 0.3, "slide": 0.2},     # 困难：更多复杂音符
        8: {"tap": 0.45, "hold": 0.35, "slide": 0.2},
        9: {"tap": 0.4, "hold": 0.35, "slide": 0.25},
        10: {"tap": 0.35, "hold": 0.4, "slide": 0.25}   # 地狱：最多复杂音符
    }

    factor = difficulty_factors.get(difficulty, 0.8)
    probs = note_type_probabilities.get(difficulty, note_type_probabilities[7])

    if factor <= 1.0:
        # 减少音符：随机移除一些音符
        mask = np.random.random(chart_array.shape) < factor
        adjusted_chart = chart_array * mask
    else:
        # 增加音符：在空白处添加音符
        adjusted_chart = chart_array.copy()
        empty_positions = (chart_array == 0)
        add_probability = factor - 1.0
        add_mask = np.random.random(chart_array.shape) < add_probability
        add_positions = empty_positions & add_mask
        adjusted_chart[add_positions] = 1  # 添加短音符

    # 重新分配音符类型，增加多样性
    for i in range(adjusted_chart.shape[0]):
        for j in range(adjusted_chart.shape[1]):
            if adjusted_chart[i, j] > 0:
                # 根据概率分布随机选择音符类型
                rand = random.random()
                if rand < probs["tap"]:
                    adjusted_chart[i, j] = 1  # tap
                elif rand < probs["tap"] + probs["hold"]:
                    adjusted_chart[i, j] = 2  # hold
                else:
                    adjusted_chart[i, j] = 3  # slide

    return adjusted_chart.astype(chart_array.dtype)

def balance_track_distribution(chart_array):
    """
    平衡轨道分布，确保每个轨道都有合理的音符数量

    Args:
        chart_array: 原始谱面数组 [time_steps, tracks]

    Returns:
        平衡后的谱面数组
    """
    import numpy as np

    # 计算当前轨道分布
    track_counts = np.sum(chart_array > 0, axis=0)
    total_notes = np.sum(track_counts)

    if total_notes == 0:
        return chart_array

    # 目标分布：每个轨道20-30%的音符（允许一定变化）
    target_min_ratio = 0.15  # 最少15%
    target_max_ratio = 0.35  # 最多35%

    current_ratios = track_counts / total_notes

    # 找出需要调整的轨道
    over_tracks = []  # 音符过多的轨道
    under_tracks = [] # 音符过少的轨道

    for track_idx in range(len(track_counts)):
        ratio = current_ratios[track_idx]
        if ratio > target_max_ratio:
            over_tracks.append(track_idx)
        elif ratio < target_min_ratio:
            under_tracks.append(track_idx)

    # 如果分布已经合理，直接返回
    if not over_tracks and not under_tracks:
        return chart_array

    # 创建平衡后的数组
    balanced_chart = chart_array.copy()

    # 从过多的轨道移动音符到过少的轨道
    for over_track in over_tracks:
        # 计算需要移除的音符数量
        current_count = track_counts[over_track]
        target_count = int(total_notes * target_max_ratio)
        excess_notes = current_count - target_count

        if excess_notes <= 0:
            continue

        # 找到该轨道的所有音符位置
        note_positions = np.where(balanced_chart[:, over_track] > 0)[0]

        # 随机选择要移除的音符
        if len(note_positions) > excess_notes:
            remove_positions = np.random.choice(note_positions, excess_notes, replace=False)
        else:
            remove_positions = note_positions

        # 移除选中的音符
        for pos in remove_positions:
            note_type = balanced_chart[pos, over_track]
            balanced_chart[pos, over_track] = 0

            # 尝试将音符移动到音符较少的轨道
            if under_tracks:
                # 选择音符最少的轨道
                target_track = min(under_tracks, key=lambda t: np.sum(balanced_chart[:, t] > 0))

                # 在相近的时间位置放置音符
                search_range = 5  # 搜索前后5个时间步
                start_pos = max(0, pos - search_range)
                end_pos = min(len(balanced_chart), pos + search_range + 1)

                # 找到目标轨道的空位
                for new_pos in range(start_pos, end_pos):
                    if balanced_chart[new_pos, target_track] == 0:
                        balanced_chart[new_pos, target_track] = note_type
                        break
                else:
                    # 如果附近没有空位，直接在原时间位置放置
                    if balanced_chart[pos, target_track] == 0:
                        balanced_chart[pos, target_track] = note_type

    # 为音符过少的轨道添加音符
    for under_track in under_tracks:
        current_count = np.sum(balanced_chart[:, under_track] > 0)
        target_count = int(total_notes * target_min_ratio)
        needed_notes = target_count - current_count

        if needed_notes <= 0:
            continue

        # 找到该轨道的空位
        empty_positions = np.where(balanced_chart[:, under_track] == 0)[0]

        if len(empty_positions) == 0:
            continue

        # 随机选择位置添加音符
        if len(empty_positions) > needed_notes:
            add_positions = np.random.choice(empty_positions, needed_notes, replace=False)
        else:
            add_positions = empty_positions

        # 添加音符（随机选择类型）
        for pos in add_positions:
            # 根据周围音符的类型选择合适的音符类型
            note_type = random.choice([1, 2, 3])  # tap, hold, slide
            balanced_chart[pos, under_track] = note_type

    return balanced_chart

def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='AI音游谱面生成器 - JSON格式输出')
    parser.add_argument('audio_path', help='音频文件路径 (MP3/WAV等)')
    parser.add_argument('output_path', help='输出JSON谱面文件路径 (建议使用.json扩展名)')
    parser.add_argument('--difficulty', '-d', type=int, default=7, choices=range(1, 11),
                       help='难度等级 (1-10，默认7)')
    parser.add_argument('--style', '-s', default='balanced',
                       choices=['balanced', 'dense', 'sparse', 'rhythmic'],
                       help='生成风格 (默认balanced)')
    parser.add_argument('--title', '-t', help='歌曲标题')
    parser.add_argument('--artist', '-a', help='艺术家')
    
    args = parser.parse_args()
    
    print("🎵 AI音游谱面生成器 - JSON格式")
    print("=" * 50)

    success = generate_chart_with_params(
        audio_path=args.audio_path,
        output_path=args.output_path,
        difficulty=args.difficulty,
        style=args.style,
        title=args.title,
        artist=args.artist
    )

    if success:
        print(f"\n🎉 JSON谱面生成成功！")
        print(f"📁 输出文件: {args.output_path}")
        print(f"🎯 难度等级: {args.difficulty}")
        print(f"📄 格式: JSON (通用格式)")
        print(f"💡 您可以:")
        print(f"   1. 查看JSON文件了解谱面结构")
        print(f"   2. 将数据转换为其他音游格式")
        print(f"   3. 在支持JSON的音游中使用")
    else:
        print(f"\n❌ 谱面生成失败")
    
    return 0 if success else 1

if __name__ == "__main__":
    exit(main())
