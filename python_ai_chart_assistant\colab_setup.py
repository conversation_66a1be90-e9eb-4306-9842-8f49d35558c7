#!/usr/bin/env python3
"""
Google Colab 一站式设置脚本
解决所有依赖、数据和训练问题
"""

import os
import shutil
import subprocess
import sys

def install_dependencies():
    """安装所有必要依赖"""
    print("📦 安装依赖包...")
    
    packages = ['pretty_midi', 'mido', 'librosa', 'tqdm', 'pyyaml']
    
    for package in packages:
        try:
            __import__(package.replace('-', '_'))
            print(f"✅ {package} 已安装")
        except ImportError:
            print(f"📦 安装 {package}...")
            subprocess.check_call([sys.executable, '-m', 'pip', 'install', package, '--quiet'])
            print(f"✅ {package} 安装完成")

def setup_real_data():
    """设置真实歌曲数据"""
    print("🎵 设置真实歌曲数据...")
    
    # 项目目录
    project_dir = "/content/drive/MyDrive/python_ai_chart_assistant"
    real_data_dir = os.path.join(project_dir, "data", "real_songs")
    
    # 检查是否已解压数据
    if os.path.exists(real_data_dir) and os.listdir(real_data_dir):
        print(f"✅ 真实数据已存在: {len(os.listdir(real_data_dir))} 个文件夹")
        return real_data_dir
    
    # 解压数据包
    os.makedirs(real_data_dir, exist_ok=True)
    
    # 查找压缩包
    archive_files = []
    for i in range(1, 12):
        archive_name = f"song_data_batch_{i:02d}.zip"
        archive_path = f"/content/drive/MyDrive/{archive_name}"
        if os.path.exists(archive_path):
            archive_files.append(archive_path)
    
    if not archive_files:
        print("❌ 未找到歌曲数据压缩包")
        print("请确保已上传 song_data_batch_*.zip 文件到Google Drive根目录")
        return None
    
    print(f"📦 找到 {len(archive_files)} 个压缩包，开始解压...")
    
    import zipfile
    total_songs = 0
    for archive_file in archive_files:
        print(f"📦 解压: {os.path.basename(archive_file)}")
        with zipfile.ZipFile(archive_file, 'r') as zipf:
            zipf.extractall(real_data_dir)
            song_folders = [name for name in zipf.namelist() if name.endswith('/') and name.count('/') == 1]
            total_songs += len(song_folders)
    
    print(f"🎵 解压完成，共 {total_songs} 首歌曲")
    return real_data_dir

def reorganize_data(source_dir, max_songs=100):
    """重组数据为训练格式"""
    print(f"🔄 重组数据为训练格式 (最多{max_songs}首)...")

    target_dir = "data/real_training_data"
    audio_dir = os.path.join(target_dir, "audio")
    charts_dir = os.path.join(target_dir, "charts")

    # 清理并创建目录
    if os.path.exists(target_dir):
        shutil.rmtree(target_dir)
    os.makedirs(audio_dir, exist_ok=True)
    os.makedirs(charts_dir, exist_ok=True)

    # 扫描歌曲文件夹
    song_folders = [d for d in os.listdir(source_dir)
                   if os.path.isdir(os.path.join(source_dir, d))]

    print(f"📁 找到 {len(song_folders)} 个歌曲文件夹")

    processed_count = 0

    chart_count = 0

    for song_folder_name in song_folders[:max_songs]:
        song_folder = os.path.join(source_dir, song_folder_name)

        # 查找音频和谱面文件
        audio_files = [f for f in os.listdir(song_folder) if f.endswith('.mp3')]
        all_chart_files = [f for f in os.listdir(song_folder) if f.endswith('.mc')]

        if audio_files and all_chart_files:
            audio_src = os.path.join(song_folder, audio_files[0])

            # 为每个谱面文件创建一个训练样本
            for chart_file in all_chart_files:
                # 使用chart_count作为唯一标识
                base_name = f"song{chart_count:03d}"

                # 复制音频文件 (每个谱面都需要对应的音频)
                audio_dst = os.path.join(audio_dir, f"{base_name}.mp3")
                shutil.copy2(audio_src, audio_dst)

                # 复制谱面文件
                chart_src = os.path.join(song_folder, chart_file)
                chart_dst = os.path.join(charts_dir, f"{base_name}.mc")
                shutil.copy2(chart_src, chart_dst)

                chart_count += 1

            processed_count += 1

            if processed_count % 10 == 0:
                print(f"   📊 已处理: {processed_count} 首歌曲, {chart_count} 个谱面")

    print(f"✅ 重组完成，处理了 {processed_count} 首歌曲, {chart_count} 个谱面")
    print(f"📁 音频文件: {len(os.listdir(audio_dir))} 个")
    print(f"📁 谱面文件: {len(os.listdir(charts_dir))} 个")
    print(f"📊 平均每首歌: {chart_count/processed_count:.1f} 个谱面")

    return target_dir

def start_training(data_dir):
    """开始训练"""
    print("🚀 开始训练...")
    
    cmd = [
        "python", "train_optimized.py",
        "--data-dir", data_dir,
        "--save-dir", "models/real_data_gan",
        "--epochs", "10",
        "--batch-size", "4"
    ]
    
    print(f"执行命令: {' '.join(cmd)}")
    
    try:
        # 使用实时输出
        process = subprocess.Popen(cmd, stdout=subprocess.PIPE, stderr=subprocess.STDOUT, 
                                 universal_newlines=True, bufsize=1)
        
        for line in process.stdout:
            print(line.rstrip())
        
        process.wait()
        
        if process.returncode == 0:
            print("🎉 训练完成!")
            return True
        else:
            print("❌ 训练失败")
            return False
            
    except Exception as e:
        print(f"❌ 训练过程出错: {e}")
        return False

def complete_setup():
    """完整设置流程"""
    print("🎯 开始完整的Colab设置...")
    print("=" * 50)
    
    # 1. 安装依赖
    install_dependencies()
    print()
    
    # 2. 设置项目路径
    project_dir = "/content/drive/MyDrive/python_ai_chart_assistant"
    if os.path.exists(project_dir):
        os.chdir(project_dir)
        if project_dir not in sys.path:
            sys.path.insert(0, project_dir)
        print(f"📂 项目目录: {project_dir}")
    else:
        print(f"❌ 项目目录不存在: {project_dir}")
        return False
    
    # 3. 设置真实数据
    real_data_dir = setup_real_data()
    if not real_data_dir:
        print("❌ 数据设置失败")
        return False
    print()
    
    # 4. 重组数据 (增加到100首)
    training_data_dir = reorganize_data(real_data_dir, max_songs=100)
    print()
    
    # 5. 开始训练
    success = start_training(training_data_dir)
    
    print("\n" + "=" * 50)
    if success:
        print("🎉 设置和训练完成!")
    else:
        print("❌ 训练失败，但环境已设置完成")
    print("=" * 50)
    
    return success

# 便捷函数
def quick_setup():
    """快速设置"""
    return complete_setup()

def quick_reorganize(max_songs=100):
    """快速重组数据"""
    print(f"🔄 快速重组数据 ({max_songs}首)...")

    project_dir = "/content/drive/MyDrive/python_ai_chart_assistant"
    real_data_dir = os.path.join(project_dir, "data", "real_songs")

    if not os.path.exists(real_data_dir):
        print("❌ 真实数据目录不存在")
        return None

    os.chdir(project_dir)
    return reorganize_data(real_data_dir, max_songs)

def quick_train():
    """快速开始训练"""
    print("🚀 快速开始训练...")

    project_dir = "/content/drive/MyDrive/python_ai_chart_assistant"
    training_data_dir = os.path.join(project_dir, "data/real_training_data")

    if not os.path.exists(training_data_dir):
        print("❌ 训练数据目录不存在，请先运行 quick_reorganize()")
        return False

    os.chdir(project_dir)
    return start_training("data/real_training_data")

# 显示使用说明
print("""
🎯 Google Colab 一站式设置
========================

使用方法:
quick_setup()              # 一键完成所有设置和训练
quick_reorganize(100)       # 重组100首歌曲数据
quick_train()              # 开始训练

包含功能:
✅ 安装所有依赖包
✅ 解压真实歌曲数据
✅ 重组数据格式 (修复文件名匹配)
✅ 开始AI训练

========================
""")

# 如果直接运行，执行完整设置
if __name__ == "__main__":
    complete_setup()
