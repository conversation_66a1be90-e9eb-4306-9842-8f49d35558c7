#!/usr/bin/env python3
"""
上传项目代码到Google Colab脚本
将本地项目文件同步到 /content/drive/MyDrive/python_ai_chart_assistant
"""

import os
import shutil
import zipfile
from pathlib import Path
import argparse

def create_project_archive():
    """创建项目压缩包，排除不必要的文件"""
    
    project_root = Path(__file__).parent
    archive_name = "python_ai_chart_assistant.zip"
    
    # 需要包含的文件和目录
    include_patterns = [
        "src/",
        "configs/",
        "*.py",
        "requirements.txt",
        "README.md",
    ]
    
    # 需要排除的文件和目录
    exclude_patterns = [
        "__pycache__/",
        "*.pyc",
        ".git/",
        ".gitignore",
        "models/",  # 模型文件太大，不上传
        "data/",    # 数据文件太大，不上传
        "*.pth",
        "*.pkl",
        "logs/",
        ".vscode/",
        "*.log",
    ]
    
    print(f"📦 创建项目压缩包: {archive_name}")
    
    with zipfile.ZipFile(archive_name, 'w', zipfile.ZIP_DEFLATED) as zipf:
        
        # 添加Python文件
        for py_file in project_root.glob("*.py"):
            if not any(pattern in str(py_file) for pattern in exclude_patterns):
                arcname = py_file.name
                zipf.write(py_file, arcname)
                print(f"  ✅ {arcname}")
        
        # 添加src目录
        src_dir = project_root / "src"
        if src_dir.exists():
            for file_path in src_dir.rglob("*"):
                if file_path.is_file() and not any(pattern in str(file_path) for pattern in exclude_patterns):
                    arcname = file_path.relative_to(project_root)
                    zipf.write(file_path, arcname)
                    print(f"  ✅ {arcname}")
        
        # 添加configs目录
        configs_dir = project_root / "configs"
        if configs_dir.exists():
            for file_path in configs_dir.rglob("*"):
                if file_path.is_file():
                    arcname = file_path.relative_to(project_root)
                    zipf.write(file_path, arcname)
                    print(f"  ✅ {arcname}")
        
        # 添加其他重要文件
        other_files = ["requirements.txt", "README.md"]
        for filename in other_files:
            file_path = project_root / filename
            if file_path.exists():
                zipf.write(file_path, filename)
                print(f"  ✅ {filename}")
    
    archive_path = project_root / archive_name
    file_size = archive_path.stat().st_size / 1024 / 1024  # MB
    print(f"📦 压缩包创建完成: {archive_path}")
    print(f"📊 文件大小: {file_size:.1f} MB")
    
    return archive_path

def generate_colab_upload_code(archive_path):
    """生成Colab上传代码"""
    
    colab_code = f'''
# Google Colab 项目上传代码
# 请在Colab中运行以下代码

# 1. 挂载Google Drive
from google.colab import drive
drive.mount('/content/drive')

# 2. 安装依赖
!pip install torch torchvision torchaudio
!pip install librosa
!pip install tqdm
!pip install pyyaml

# 3. 创建项目目录
import os
project_dir = "/content/drive/MyDrive/python_ai_chart_assistant"
os.makedirs(project_dir, exist_ok=True)
print(f"📁 项目目录: {{project_dir}}")

# 4. 上传压缩包
# 请手动上传 {archive_path.name} 到 /content/drive/MyDrive/

# 5. 解压项目文件
import zipfile
import shutil

archive_path = "/content/drive/MyDrive/{archive_path.name}"
if os.path.exists(archive_path):
    print("📦 解压项目文件...")
    with zipfile.ZipFile(archive_path, 'r') as zipf:
        zipf.extractall(project_dir)
    print("✅ 解压完成!")
    
    # 列出文件
    print("📋 项目文件:")
    for root, dirs, files in os.walk(project_dir):
        level = root.replace(project_dir, '').count(os.sep)
        indent = ' ' * 2 * level
        print(f"{{indent}}{{os.path.basename(root)}}/")
        subindent = ' ' * 2 * (level + 1)
        for file in files:
            print(f"{{subindent}}{{file}}")
else:
    print(f"❌ 未找到压缩包: {{archive_path}}")
    print("请先上传压缩包到Google Drive")

# 6. 设置工作目录
os.chdir(project_dir)
print(f"📂 当前工作目录: {{os.getcwd()}}")

# 7. 测试导入
try:
    import sys
    sys.path.append(project_dir)
    
    from src.models.audio_chart_gan import AudioChartGAN
    from src.training.audio_chart_dataset import AudioChartDataset
    print("✅ 模块导入成功!")
except Exception as e:
    print(f"❌ 模块导入失败: {{e}}")

# 8. 创建数据目录
data_dir = os.path.join(project_dir, "data")
models_dir = os.path.join(project_dir, "models")
os.makedirs(data_dir, exist_ok=True)
os.makedirs(models_dir, exist_ok=True)
print(f"📁 数据目录: {{data_dir}}")
print(f"📁 模型目录: {{models_dir}}")

print("🎉 项目设置完成! 现在可以开始训练了")
'''
    
    # 保存Colab代码到文件
    colab_file = archive_path.parent / "colab_setup.py"
    with open(colab_file, 'w', encoding='utf-8') as f:
        f.write(colab_code)
    
    print(f"📝 Colab设置代码已保存: {colab_file}")
    return colab_file

def generate_colab_training_code():
    """生成Colab训练代码"""
    
    training_code = '''
# Google Colab 训练代码
# 在项目设置完成后运行

import torch
import os

# 检查GPU
device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
print(f"🚀 使用设备: {device}")
if torch.cuda.is_available():
    print(f"GPU: {torch.cuda.get_device_name(0)}")
    print(f"显存: {torch.cuda.get_device_properties(0).total_memory / 1024**3:.1f} GB")

# 设置项目路径
project_dir = "/content/drive/MyDrive/python_ai_chart_assistant"
os.chdir(project_dir)

# 创建示例数据（如果没有真实数据）
def create_dummy_data():
    """创建示例数据用于测试"""
    import numpy as np
    import pickle
    
    data_dir = os.path.join(project_dir, "data", "training_demo")
    os.makedirs(data_dir, exist_ok=True)
    
    # 创建示例音频特征缓存
    dummy_features = {}
    for i in range(10):  # 10个示例文件
        # 94维特征，2000时间步
        features = torch.randn(94, 2000)
        dummy_features[f"demo_song_{i}.mp3"] = features
    
    cache_file = os.path.join(data_dir, "audio_features_cache.pkl")
    with open(cache_file, 'wb') as f:
        pickle.dump(dummy_features, f)
    
    print(f"✅ 示例数据创建完成: {data_dir}")
    return data_dir

# 如果没有真实数据，创建示例数据
data_dir = "/content/drive/MyDrive/python_ai_chart_assistant/data/training_demo"
if not os.path.exists(data_dir):
    print("📊 创建示例数据...")
    data_dir = create_dummy_data()

# 运行快速训练测试
print("🚀 开始快速训练测试...")
!python train_optimized.py --data-dir "{data_dir}" --save-dir "models/colab_test" --epochs 2 --batch-size 4

print("🎉 Colab训练测试完成!")
'''
    
    training_file = Path(__file__).parent / "colab_training.py"
    with open(training_file, 'w', encoding='utf-8') as f:
        f.write(training_code)
    
    print(f"📝 Colab训练代码已保存: {training_file}")
    return training_file

def main():
    """主函数"""
    parser = argparse.ArgumentParser(description="上传项目到Google Colab")
    parser.add_argument("--create-archive", action="store_true", help="创建项目压缩包")
    parser.add_argument("--generate-codes", action="store_true", help="生成Colab代码")
    parser.add_argument("--all", action="store_true", help="执行所有操作")
    
    args = parser.parse_args()
    
    if args.all or args.create_archive:
        print("🚀 开始创建项目压缩包...")
        archive_path = create_project_archive()
    else:
        archive_path = Path(__file__).parent / "python_ai_chart_assistant.zip"
    
    if args.all or args.generate_codes:
        print("📝 生成Colab代码...")
        colab_file = generate_colab_upload_code(archive_path)
        training_file = generate_colab_training_code()
        
        print("\n" + "="*60)
        print("🎯 上传到Colab的步骤:")
        print("="*60)
        print(f"1. 将 {archive_path.name} 上传到Google Drive根目录")
        print(f"2. 在Colab中运行 {colab_file.name} 的代码")
        print(f"3. 运行 {training_file.name} 的代码开始训练")
        print("="*60)
    
    if not any([args.create_archive, args.generate_codes, args.all]):
        print("请指定操作: --create-archive, --generate-codes, 或 --all")

if __name__ == "__main__":
    main()
