<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AI音游谱面查看器</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: rgba(255, 255, 255, 0.95);
            border-radius: 15px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            overflow: hidden;
        }

        .header {
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }

        .header h1 {
            font-size: 2.5em;
            margin-bottom: 10px;
            text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
        }

        .file-input {
            margin: 20px 0;
        }

        .file-input input[type="file"] {
            display: none;
        }

        .file-input label {
            display: inline-block;
            padding: 12px 24px;
            background: rgba(255, 255, 255, 0.2);
            border: 2px dashed rgba(255, 255, 255, 0.5);
            border-radius: 8px;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .file-input label:hover {
            background: rgba(255, 255, 255, 0.3);
            border-color: rgba(255, 255, 255, 0.8);
        }

        .content {
            padding: 30px;
        }

        .metadata-section {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 25px;
            margin-bottom: 30px;
            border-left: 5px solid #4facfe;
        }

        .metadata-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin-top: 20px;
        }

        .metadata-item {
            background: white;
            padding: 15px;
            border-radius: 8px;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }

        .metadata-item .label {
            font-weight: bold;
            color: #666;
            font-size: 0.9em;
            margin-bottom: 5px;
        }

        .metadata-item .value {
            font-size: 1.2em;
            color: #333;
        }

        .chart-section {
            margin-top: 30px;
        }

        .chart-controls {
            display: flex;
            gap: 15px;
            margin-bottom: 20px;
            flex-wrap: wrap;
        }

        .control-group {
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .control-group label {
            font-weight: bold;
            color: #555;
        }

        .control-group input, .control-group select {
            padding: 8px 12px;
            border: 2px solid #ddd;
            border-radius: 5px;
            font-size: 14px;
        }

        .chart-container {
            background: #fff;
            border-radius: 10px;
            padding: 20px;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
            overflow-x: auto;
        }

        .chart-timeline {
            position: relative;
            height: 600px;
            border: 2px solid #eee;
            border-radius: 8px;
            background: linear-gradient(to right, #f8f9fa 0%, #fff 100%);
            overflow: auto;
        }

        .track-header {
            display: flex;
            background: #343a40;
            color: white;
            font-weight: bold;
        }

        .track-label {
            width: 80px;
            padding: 15px 10px;
            text-align: center;
            border-right: 1px solid #555;
        }

        .track-area {
            flex: 1;
            position: relative;
            border-right: 1px solid #555;
        }

        .tracks-container {
            display: flex;
            position: relative;
            min-height: 100%;
        }

        .track {
            flex: 1;
            position: relative;
            border-right: 1px solid #ddd;
            background: repeating-linear-gradient(
                to bottom,
                transparent 0px,
                transparent 24px,
                rgba(0, 0, 0, 0.05) 24px,
                rgba(0, 0, 0, 0.05) 25px
            );
            min-height: 100%;
        }

        .track:last-child {
            border-right: none;
        }

        .note {
            position: absolute;
            width: 90%;
            left: 5%;
            border-radius: 4px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 12px;
            font-weight: bold;
            color: white;
            text-shadow: 0 1px 2px rgba(0, 0, 0, 0.5);
            cursor: pointer;
            transition: all 0.2s ease;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
        }

        .note:hover {
            transform: scale(1.05);
            z-index: 10;
        }

        .note.tap {
            background: linear-gradient(135deg, #ff6b6b, #ee5a52);
            height: 20px;
        }

        .note.hold {
            background: linear-gradient(135deg, #4ecdc4, #44a08d);
            min-height: 30px;
        }

        .note.slide {
            background: linear-gradient(135deg, #feca57, #ff9ff3);
            height: 20px;
        }

        .time-ruler {
            position: absolute;
            left: 0;
            right: 0;
            height: 100%;
            pointer-events: none;
        }

        .time-mark {
            position: absolute;
            left: 0;
            right: 0;
            border-top: 1px solid rgba(0, 0, 0, 0.1);
            font-size: 10px;
            color: #666;
            padding-left: 5px;
        }

        .statistics {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-top: 30px;
        }

        .stat-card {
            background: white;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
            text-align: center;
        }

        .stat-card .number {
            font-size: 2em;
            font-weight: bold;
            color: #4facfe;
            margin-bottom: 5px;
        }

        .stat-card .label {
            color: #666;
            font-size: 0.9em;
        }

        .legend {
            display: flex;
            gap: 20px;
            margin-bottom: 20px;
            flex-wrap: wrap;
        }

        .legend-item {
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .legend-color {
            width: 20px;
            height: 20px;
            border-radius: 4px;
        }

        .error-message {
            background: #f8d7da;
            color: #721c24;
            padding: 15px;
            border-radius: 8px;
            margin: 20px 0;
            border: 1px solid #f5c6cb;
        }

        .loading {
            text-align: center;
            padding: 40px;
            color: #666;
        }

        @media (max-width: 768px) {
            .metadata-grid {
                grid-template-columns: 1fr;
            }
            
            .chart-controls {
                flex-direction: column;
            }
            
            .statistics {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🎵 AI音游谱面查看器</h1>
            <p>可视化查看AI生成的音游谱面文件</p>
            
            <div class="file-input">
                <label for="jsonFile">
                    📁 选择JSON谱面文件
                </label>
                <input type="file" id="jsonFile" accept=".json" />
            </div>
        </div>

        <div class="content">
            <div id="errorMessage" class="error-message" style="display: none;"></div>
            <div id="loadingMessage" class="loading" style="display: none;">正在加载谱面数据...</div>
            
            <div id="chartContent" style="display: none;">
                <!-- 元数据部分 -->
                <div class="metadata-section">
                    <h2>📊 谱面信息</h2>
                    <div class="metadata-grid" id="metadataGrid"></div>
                </div>

                <!-- 谱面可视化部分 -->
                <div class="chart-section">
                    <h2>🎼 谱面预览</h2>
                    
                    <div class="chart-controls">
                        <div class="control-group">
                            <label>显示模式:</label>
                            <select id="displayMode">
                                <option value="all" selected>显示所有音符</option>
                                <option value="simplified">简化显示</option>
                                <option value="density">密度热图</option>
                                <option value="no-overlap">去重叠显示</option>
                            </select>
                        </div>

                        <div class="control-group">
                            <label>时间缩放:</label>
                            <select id="timeScale">
                                <option value="0.5">0.5x (压缩)</option>
                                <option value="1" selected>1x (正常)</option>
                                <option value="2">2x (拉伸)</option>
                                <option value="4">4x (详细)</option>
                                <option value="8">8x (超详细)</option>
                            </select>
                        </div>

                        <div class="control-group">
                            <label>音符大小:</label>
                            <select id="noteSize">
                                <option value="small">小</option>
                                <option value="medium" selected>中</option>
                                <option value="large">大</option>
                            </select>
                        </div>

                        <div class="control-group">
                            <button id="scrollToTop" style="padding: 8px 16px; background: #4facfe; color: white; border: none; border-radius: 4px; cursor: pointer;">⬆️ 回到顶部</button>
                            <button id="scrollToEnd" style="padding: 8px 16px; background: #4facfe; color: white; border: none; border-radius: 4px; cursor: pointer;">⬇️ 到结尾</button>
                        </div>
                    </div>

                    <div class="legend">
                        <div class="legend-item">
                            <div class="legend-color" style="background: linear-gradient(135deg, #ff6b6b, #ee5a52);"></div>
                            <span>Tap 短音符</span>
                        </div>
                        <div class="legend-item">
                            <div class="legend-color" style="background: linear-gradient(135deg, #4ecdc4, #44a08d);"></div>
                            <span>Hold 长音符</span>
                        </div>
                        <div class="legend-item">
                            <div class="legend-color" style="background: linear-gradient(135deg, #feca57, #ff9ff3);"></div>
                            <span>Slide 滑动音符</span>
                        </div>
                    </div>

                    <div class="chart-container">
                        <div class="track-header">
                            <div class="track-label">时间</div>
                            <div class="track-area">轨道 1</div>
                            <div class="track-area">轨道 2</div>
                            <div class="track-area">轨道 3</div>
                            <div class="track-area">轨道 4</div>
                        </div>
                        <div class="chart-timeline" id="chartTimeline">
                            <div class="time-ruler" id="timeRuler"></div>
                            <div class="tracks-container">
                                <div class="track" data-track="1"></div>
                                <div class="track" data-track="2"></div>
                                <div class="track" data-track="3"></div>
                                <div class="track" data-track="4"></div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 统计信息 -->
                <div class="statistics" id="statistics"></div>
            </div>
        </div>
    </div>

    <script>
        let chartData = null;
        let displayMode = 'all';
        let timeScale = 1;
        let noteSize = 'medium';

        // 文件选择处理
        document.getElementById('jsonFile').addEventListener('change', function(e) {
            const file = e.target.files[0];
            if (file) {
                loadChartFile(file);
            }
        });

        // 自动加载output.json文件
        window.addEventListener('load', function() {
            loadDefaultChart();
        });

        async function loadDefaultChart() {
            try {
                showLoading(true);
                const response = await fetch('output.json');
                if (response.ok) {
                    const data = await response.json();
                    processChartData(data);
                } else {
                    showError('无法加载默认谱面文件 output.json');
                }
            } catch (error) {
                showError('加载默认谱面文件失败: ' + error.message);
            } finally {
                showLoading(false);
            }
        }

        function loadChartFile(file) {
            showLoading(true);
            const reader = new FileReader();
            
            reader.onload = function(e) {
                try {
                    const data = JSON.parse(e.target.result);
                    processChartData(data);
                } catch (error) {
                    showError('JSON文件格式错误: ' + error.message);
                } finally {
                    showLoading(false);
                }
            };
            
            reader.onerror = function() {
                showError('文件读取失败');
                showLoading(false);
            };
            
            reader.readAsText(file);
        }

        function processChartData(data) {
            chartData = data;
            hideError();
            
            // 显示内容
            document.getElementById('chartContent').style.display = 'block';
            
            // 渲染元数据
            renderMetadata(data.metadata);
            
            // 设置时间范围控制
            setupTimeControls(data.metadata.duration);
            
            // 渲染谱面
            renderChart();
            
            // 渲染统计信息
            renderStatistics(data);
        }

        function renderMetadata(metadata) {
            const grid = document.getElementById('metadataGrid');
            grid.innerHTML = '';
            
            const items = [
                { label: '歌曲标题', value: metadata.title },
                { label: '艺术家', value: metadata.artist },
                { label: 'BPM', value: metadata.bpm.toFixed(1) },
                { label: '难度等级', value: metadata.difficulty + '/10' },
                { label: '总时长', value: metadata.duration.toFixed(1) + '秒' },
                { label: '音符总数', value: metadata.total_notes.toLocaleString() },
                { label: '音符密度', value: metadata.note_density.toFixed(2) + ' 音符/秒' },
                { label: '生成风格', value: metadata.style },
                { label: '生成时间', value: new Date(metadata.generated_time).toLocaleString() },
                { label: 'AI模型', value: metadata.ai_model }
            ];
            
            items.forEach(item => {
                const div = document.createElement('div');
                div.className = 'metadata-item';
                div.innerHTML = `
                    <div class="label">${item.label}</div>
                    <div class="value">${item.value}</div>
                `;
                grid.appendChild(div);
            });
        }

        function setupTimeControls(duration) {
            // 事件监听
            document.getElementById('displayMode').addEventListener('change', function() {
                displayMode = this.value;
                renderChart();
            });

            document.getElementById('timeScale').addEventListener('change', function() {
                timeScale = parseFloat(this.value);
                renderChart();
            });

            document.getElementById('noteSize').addEventListener('change', function() {
                noteSize = this.value;
                renderChart();
            });

            // 滚动按钮
            document.getElementById('scrollToTop').addEventListener('click', function() {
                document.getElementById('chartTimeline').scrollTop = 0;
            });

            document.getElementById('scrollToEnd').addEventListener('click', function() {
                const timeline = document.getElementById('chartTimeline');
                timeline.scrollTop = timeline.scrollHeight;
            });
        }



        function renderChart() {
            if (!chartData) return;

            const timeline = document.getElementById('chartTimeline');
            const timeRuler = document.getElementById('timeRuler');
            const tracks = timeline.querySelectorAll('.track');
            const tracksContainer = timeline.querySelector('.tracks-container');

            // 清空现有内容
            tracks.forEach(track => {
                track.innerHTML = '';
            });
            timeRuler.innerHTML = '';

            // 使用完整的音乐时长
            const totalDuration = chartData.metadata.duration * 1000; // 转换为毫秒
            let allNotes = chartData.notes;

            // 根据显示模式处理音符
            if (displayMode === 'simplified') {
                allNotes = simplifyNotes(allNotes, totalDuration);
            } else if (displayMode === 'density') {
                renderDensityHeatmap(timeline, allNotes, 0, totalDuration, null);
                return;
            }

            // 计算画布高度 - 根据音乐总长度和时间缩放
            const pixelsPerSecond = 50 * timeScale; // 每秒50像素 * 缩放倍数
            const totalHeight = Math.max(600, (totalDuration / 1000) * pixelsPerSecond);
            const pixelsPerMs = pixelsPerSecond / 1000;

            // 设置轨道容器高度
            tracksContainer.style.height = totalHeight + 'px';
            timeRuler.style.height = totalHeight + 'px';

            // 渲染时间标尺 - 每5秒一个标记
            const timeStep = 5000; // 5秒间隔
            for (let time = 0; time <= totalDuration; time += timeStep) {
                const y = time * pixelsPerMs;
                const mark = document.createElement('div');
                mark.className = 'time-mark';
                mark.style.top = y + 'px';
                mark.textContent = (time / 1000).toFixed(0) + 's';
                timeRuler.appendChild(mark);
            }

            // 渲染所有音符
            allNotes.forEach(note => {
                const trackIndex = note.track - 1;
                if (trackIndex >= 0 && trackIndex < tracks.length) {
                    const track = tracks[trackIndex];
                    const noteElement = createNoteElement(note, 0, pixelsPerMs, totalHeight);
                    if (noteElement) {
                        track.appendChild(noteElement);
                    }
                }
            });
        }

        function createNoteElement(note, startTime, pixelsPerMs, totalHeight) {
            const y = (note.time - startTime) * pixelsPerMs;

            const noteEl = document.createElement('div');
            noteEl.className = `note ${note.type}`;
            noteEl.style.top = y + 'px';

            // 根据音符大小设置尺寸
            let baseHeight = 8;
            let fontSize = '10px';

            switch (noteSize) {
                case 'small':
                    baseHeight = 6;
                    fontSize = '8px';
                    break;
                case 'medium':
                    baseHeight = 8;
                    fontSize = '10px';
                    break;
                case 'large':
                    baseHeight = 12;
                    fontSize = '12px';
                    break;
            }

            // 设置音符高度和内容
            let height = baseHeight;
            let content = '';

            switch (note.type) {
                case 'tap':
                    content = 'T';
                    break;
                case 'hold':
                    height = Math.max(baseHeight * 2, (note.duration || 500) * pixelsPerMs);
                    content = 'H';
                    break;
                case 'slide':
                    content = `S→${note.target_track || '?'}`;
                    break;
            }

            noteEl.style.height = height + 'px';
            noteEl.style.fontSize = fontSize;
            noteEl.textContent = content;

            // 添加点击事件显示详细信息
            noteEl.addEventListener('click', function() {
                showNoteDetails(note);
            });

            return noteEl;
        }

        function showNoteDetails(note) {
            const details = [
                `时间: ${(note.time / 1000).toFixed(3)}秒`,
                `轨道: ${note.track}`,
                `类型: ${note.type}`,
                `持续时间: ${note.duration}ms`
            ];

            if (note.target_track) {
                details.push(`目标轨道: ${note.target_track}`);
            }

            alert(details.join('\n'));
        }

        function renderStatistics(data) {
            const statsContainer = document.getElementById('statistics');
            statsContainer.innerHTML = '';

            // 基本统计
            const basicStats = [
                { label: '总音符数', value: data.metadata.total_notes.toLocaleString(), color: '#4facfe' },
                { label: '平均密度', value: data.metadata.note_density.toFixed(2) + '/秒', color: '#7b68ee' },
                { label: '总时长', value: data.metadata.duration.toFixed(1) + '秒', color: '#20bf6b' },
                { label: 'BPM', value: data.metadata.bpm.toFixed(1), color: '#fa8231' }
            ];

            basicStats.forEach(stat => {
                const card = document.createElement('div');
                card.className = 'stat-card';
                card.innerHTML = `
                    <div class="number" style="color: ${stat.color}">${stat.value}</div>
                    <div class="label">${stat.label}</div>
                `;
                statsContainer.appendChild(card);
            });

            // 音符类型统计
            const noteTypes = { tap: 0, hold: 0, slide: 0 };
            data.notes.forEach(note => {
                if (noteTypes.hasOwnProperty(note.type)) {
                    noteTypes[note.type]++;
                }
            });

            Object.entries(noteTypes).forEach(([type, count]) => {
                const percentage = ((count / data.metadata.total_notes) * 100).toFixed(1);
                const card = document.createElement('div');
                card.className = 'stat-card';

                let typeName = '';
                let color = '';
                switch (type) {
                    case 'tap': typeName = 'Tap 短音符'; color = '#ff6b6b'; break;
                    case 'hold': typeName = 'Hold 长音符'; color = '#4ecdc4'; break;
                    case 'slide': typeName = 'Slide 滑动'; color = '#feca57'; break;
                }

                card.innerHTML = `
                    <div class="number" style="color: ${color}">${count}</div>
                    <div class="label">${typeName} (${percentage}%)</div>
                `;
                statsContainer.appendChild(card);
            });

            // 轨道分布统计
            const trackCounts = [0, 0, 0, 0];
            data.notes.forEach(note => {
                if (note.track >= 1 && note.track <= 4) {
                    trackCounts[note.track - 1]++;
                }
            });

            trackCounts.forEach((count, index) => {
                const percentage = ((count / data.metadata.total_notes) * 100).toFixed(1);
                const card = document.createElement('div');
                card.className = 'stat-card';
                card.innerHTML = `
                    <div class="number" style="color: #845ec2">${count}</div>
                    <div class="label">轨道 ${index + 1} (${percentage}%)</div>
                `;
                statsContainer.appendChild(card);
            });
        }

        function showError(message) {
            const errorEl = document.getElementById('errorMessage');
            errorEl.textContent = message;
            errorEl.style.display = 'block';
        }

        function hideError() {
            document.getElementById('errorMessage').style.display = 'none';
        }

        function showLoading(show) {
            document.getElementById('loadingMessage').style.display = show ? 'block' : 'none';
        }

        function simplifyNotes(notes, timeRange) {
            // 如果音符太密集，进行采样
            const maxNotesPerSecond = 8; // 每秒最多显示8个音符
            const maxNotes = Math.floor((timeRange / 1000) * maxNotesPerSecond);

            if (notes.length <= maxNotes) {
                return notes;
            }

            // 按时间排序并均匀采样
            const sortedNotes = notes.sort((a, b) => a.time - b.time);
            const step = Math.floor(sortedNotes.length / maxNotes);
            const simplified = [];

            for (let i = 0; i < sortedNotes.length; i += step) {
                simplified.push(sortedNotes[i]);
            }

            return simplified;
        }

        function renderDensityHeatmap(timeline, notes, startTime, totalDuration, height) {
            const tracks = timeline.querySelectorAll('.track');
            const tracksContainer = timeline.querySelector('.tracks-container');

            // 计算画布高度
            const pixelsPerSecond = 50 * timeScale;
            const totalHeight = Math.max(600, (totalDuration / 1000) * pixelsPerSecond);

            // 设置容器高度
            tracksContainer.style.height = totalHeight + 'px';
            document.getElementById('timeRuler').style.height = totalHeight + 'px';

            // 将时间分成小段，计算每段的音符密度
            const segmentDuration = 1000; // 1秒一段
            const segments = Math.ceil(totalDuration / segmentDuration);

            for (let trackIndex = 0; trackIndex < 4; trackIndex++) {
                const track = tracks[trackIndex];

                for (let seg = 0; seg < segments; seg++) {
                    const segStart = seg * segmentDuration;
                    const segEnd = segStart + segmentDuration;

                    // 计算这个时间段内该轨道的音符数量
                    const segmentNotes = notes.filter(note =>
                        note.track === trackIndex + 1 &&
                        note.time >= segStart &&
                        note.time < segEnd
                    );

                    if (segmentNotes.length > 0) {
                        const density = Math.min(segmentNotes.length / 10, 1); // 标准化到0-1
                        const y = (segStart / totalDuration) * totalHeight;
                        const segmentHeight = (segmentDuration / totalDuration) * totalHeight;

                        const heatElement = document.createElement('div');
                        heatElement.style.position = 'absolute';
                        heatElement.style.top = y + 'px';
                        heatElement.style.left = '0';
                        heatElement.style.right = '0';
                        heatElement.style.height = segmentHeight + 'px';
                        heatElement.style.backgroundColor = `rgba(255, 0, 0, ${density * 0.7})`;
                        heatElement.style.border = '1px solid rgba(255, 255, 255, 0.3)';
                        heatElement.title = `${(segStart/1000).toFixed(1)}s: ${segmentNotes.length} 音符`;

                        track.appendChild(heatElement);
                    }
                }
            }
        }
    </script>
</body>
</html>
