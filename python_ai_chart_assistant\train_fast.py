#!/usr/bin/env python3
"""
快速训练脚本 - 性能优化版本
包含所有性能优化：预提取特征、批次优化、内存管理等
"""

import argparse
import sys
import logging
from pathlib import Path
import torch
import torch.nn as nn
import torch.optim as optim
from torch.utils.data import DataLoader, random_split
from tqdm import tqdm
import gc
import time

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from src.training.audio_chart_dataset import AudioChartDataset
from src.models.audio_chart_gan import AudioChartGAN

# 设置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def optimize_pytorch():
    """PyTorch性能优化设置"""
    # 启用cudnn基准测试
    torch.backends.cudnn.benchmark = True
    
    # 设置线程数
    torch.set_num_threads(4)
    
    # 禁用调试API以提升性能
    torch.autograd.set_detect_anomaly(False)
    
    print("✅ PyTorch性能优化已启用")

def fast_train_step(model, real_charts, audio_features, target_quality, target_difficulty, 
                   optimizer_d, optimizer_g, device, loss_weights=None):
    """快速训练步骤 - 简化版本，专注性能"""
    batch_size = real_charts.size(0)
    
    # 默认损失权重
    if loss_weights is None:
        loss_weights = {
            'adversarial': 1.0,
            'quality': 0.5,
            'difficulty': 0.3,
            'music_matching': 0.8,
        }
    
    # ===== 训练判别器 =====
    optimizer_d.zero_grad()
    
    # 真实数据
    real_output = model.discriminator(real_charts)
    
    # 质量损失
    quality_target = target_quality.float().unsqueeze(1) if target_quality.dim() == 1 else target_quality.float()
    quality_loss = nn.SmoothL1Loss()(real_output['quality'], quality_target)
    
    # 难度损失
    difficulty_target = target_difficulty.long()
    difficulty_loss = nn.CrossEntropyLoss()(real_output['difficulty'], difficulty_target)
    
    # 假数据
    with torch.no_grad():
        fake_charts_probs = model.generator(audio_features)
        fake_charts = model._constrained_sampling(fake_charts_probs)
    
    fake_output = model.discriminator(fake_charts)
    
    # 标准GAN损失（更快）
    real_labels = torch.ones(batch_size, 1, device=device)
    fake_labels = torch.zeros(batch_size, 1, device=device)
    d_real_loss = nn.BCELoss()(real_output['real_fake'], real_labels)
    d_fake_loss = nn.BCELoss()(fake_output['real_fake'], fake_labels)
    d_adversarial_loss = (d_real_loss + d_fake_loss) / 2
    
    # 总判别器损失
    d_loss = (loss_weights['adversarial'] * d_adversarial_loss + 
              loss_weights['quality'] * quality_loss + 
              loss_weights['difficulty'] * difficulty_loss)
    
    d_loss.backward()
    torch.nn.utils.clip_grad_norm_(model.discriminator.parameters(), max_norm=1.0)
    optimizer_d.step()
    
    # ===== 训练生成器 =====
    optimizer_g.zero_grad()
    
    # 重新生成假数据
    fake_charts_probs = model.generator(audio_features)
    fake_charts = model._constrained_sampling(fake_charts_probs)
    fake_output = model.discriminator(fake_charts)
    
    # 生成器损失
    g_adversarial_loss = nn.BCELoss()(fake_output['real_fake'], real_labels)
    
    # 音乐匹配损失
    music_loss = model._music_matching_loss(fake_charts, audio_features)
    
    # 总生成器损失
    total_g_loss = (loss_weights['adversarial'] * g_adversarial_loss + 
                    loss_weights['music_matching'] * music_loss)
    
    total_g_loss.backward()
    torch.nn.utils.clip_grad_norm_(model.generator.parameters(), max_norm=1.0)
    optimizer_g.step()
    
    return {
        'd_loss': d_loss.item(),
        'g_loss': total_g_loss.item(),
        'music_loss': music_loss.item(),
        'quality_loss': quality_loss.item(),
        'difficulty_loss': difficulty_loss.item(),
    }

def train_fast_gan(data_dir, save_dir, epochs=10, batch_size=16, lr=0.0002):
    """快速GAN训练 - 性能优化版本"""
    
    # 性能优化设置
    optimize_pytorch()
    
    device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
    print(f"🚀 使用设备: {device}")
    
    # 检查预提取特征
    cache_file = Path(data_dir) / "audio_features_cache.pkl"
    if not cache_file.exists():
        print("⚠️  未找到音频特征缓存，建议先运行:")
        print(f"   python preprocess_audio.py --data-dir {data_dir}")
        print("🔄 继续使用实时特征提取...")
    else:
        print(f"✅ 使用音频特征缓存: {cache_file}")
    
    # 创建数据集 - 禁用增强
    print("📊 创建数据集...")
    dataset = AudioChartDataset(data_dir, augment=False)
    print(f"✅ 数据集: {len(dataset)} 个样本")
    
    # 分割数据集
    total_size = len(dataset)
    train_size = int(total_size * 0.8)
    val_size = total_size - train_size
    train_dataset, val_dataset = random_split(dataset, [train_size, val_size])
    
    # 优化的DataLoader
    num_workers = min(4, batch_size)
    train_loader = DataLoader(
        train_dataset, 
        batch_size=batch_size, 
        shuffle=True, 
        num_workers=num_workers,
        pin_memory=True if device.type == 'cuda' else False,
        persistent_workers=True if num_workers > 0 else False,
        drop_last=True  # 丢弃最后不完整的批次
    )
    
    print(f"🎯 训练配置:")
    print(f"   - 训练集: {len(train_dataset)}")
    print(f"   - 验证集: {len(val_dataset)}")
    print(f"   - 批次大小: {batch_size}")
    print(f"   - 数据加载线程: {num_workers}")
    
    # 创建模型
    print("🧠 创建模型...")
    model = AudioChartGAN(track_count=4, max_length=2000, use_wgan=False).to(device)  # 使用标准GAN更快
    
    # 优化器
    optimizer_d = optim.Adam(model.discriminator.parameters(), lr=lr, betas=(0.5, 0.999))
    optimizer_g = optim.Adam(model.generator.parameters(), lr=lr, betas=(0.5, 0.999))
    
    print(f"🎯 开始快速训练 {epochs} 轮...")
    
    # 训练循环
    start_time = time.time()
    
    for epoch in range(epochs):
        model.train()
        epoch_d_loss = 0
        epoch_g_loss = 0
        epoch_music_loss = 0
        
        progress_bar = tqdm(train_loader, desc=f"Epoch {epoch+1}/{epochs}")
        
        for batch_idx, batch in enumerate(progress_bar):
            real_charts = batch['chart'].to(device, non_blocking=True)
            audio_features = batch['audio_features'].to(device, non_blocking=True)
            target_quality = batch['quality'].to(device, non_blocking=True)
            target_difficulty = batch['difficulty'].to(device, non_blocking=True)
            
            # 快速训练步骤
            losses = fast_train_step(
                model, real_charts, audio_features, target_quality, target_difficulty,
                optimizer_d, optimizer_g, device
            )
            
            epoch_d_loss += losses['d_loss']
            epoch_g_loss += losses['g_loss']
            epoch_music_loss += losses['music_loss']
            
            # 更新进度条
            progress_bar.set_postfix({
                'D': f"{losses['d_loss']:.3f}",
                'G': f"{losses['g_loss']:.3f}",
                'M': f"{losses['music_loss']:.3f}"
            })
            
            # 定期清理显存
            if batch_idx % 10 == 0:
                torch.cuda.empty_cache()
        
        # Epoch统计
        avg_d_loss = epoch_d_loss / len(train_loader)
        avg_g_loss = epoch_g_loss / len(train_loader)
        avg_music_loss = epoch_music_loss / len(train_loader)
        
        elapsed_time = time.time() - start_time
        epoch_time = elapsed_time / (epoch + 1)
        remaining_time = epoch_time * (epochs - epoch - 1)
        
        print(f"Epoch {epoch+1}/{epochs} 完成:")
        print(f"  判别器损失: {avg_d_loss:.4f}")
        print(f"  生成器损失: {avg_g_loss:.4f}")
        print(f"  音乐匹配损失: {avg_music_loss:.4f}")
        print(f"  用时: {epoch_time:.1f}s, 剩余: {remaining_time/60:.1f}min")
        
        # 保存模型
        if (epoch + 1) % 5 == 0:
            save_path = Path(save_dir) / f"fast_gan_epoch_{epoch+1}.pth"
            save_path.parent.mkdir(parents=True, exist_ok=True)
            torch.save({
                'epoch': epoch + 1,
                'model_state_dict': model.state_dict(),
                'optimizer_d_state_dict': optimizer_d.state_dict(),
                'optimizer_g_state_dict': optimizer_g.state_dict(),
            }, save_path)
            print(f"💾 模型已保存: {save_path}")
        
        # 清理内存
        gc.collect()
        torch.cuda.empty_cache()
    
    total_time = time.time() - start_time
    print(f"🎉 训练完成！总用时: {total_time/60:.1f}分钟")
    print(f"⚡ 平均每轮: {total_time/epochs:.1f}秒")

if __name__ == "__main__":
    parser = argparse.ArgumentParser(description="快速GAN训练")
    parser.add_argument("--data-dir", required=True, help="数据目录")
    parser.add_argument("--save-dir", required=True, help="保存目录")
    parser.add_argument("--epochs", type=int, default=10, help="训练轮数")
    parser.add_argument("--batch-size", type=int, default=16, help="批次大小")
    parser.add_argument("--lr", type=float, default=0.0002, help="学习率")
    
    args = parser.parse_args()
    
    try:
        train_fast_gan(
            data_dir=args.data_dir,
            save_dir=args.save_dir,
            epochs=args.epochs,
            batch_size=args.batch_size,
            lr=args.lr
        )
    except Exception as e:
        logger.error(f"训练失败: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)
