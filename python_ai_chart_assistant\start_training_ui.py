#!/usr/bin/env python3
"""
启动训练功能的UI界面

专门用于模型训练的简化界面
"""

import streamlit as st
import sys
import subprocess
from pathlib import Path
import json
import time

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

# 页面配置
st.set_page_config(
    page_title="🚀 AI音游谱面训练器",
    page_icon="🚀",
    layout="wide",
    initial_sidebar_state="expanded"
)

# 自定义CSS
st.markdown("""
<style>
    .main-header {
        font-size: 2.5rem;
        color: #1f77b4;
        text-align: center;
        margin-bottom: 2rem;
    }
    .success-box {
        padding: 1rem;
        border-radius: 0.5rem;
        background-color: #d4edda;
        border: 1px solid #c3e6cb;
        color: #155724;
    }
    .error-box {
        padding: 1rem;
        border-radius: 0.5rem;
        background-color: #f8d7da;
        border: 1px solid #f5c6cb;
        color: #721c24;
    }
</style>
""", unsafe_allow_html=True)

def main():
    """主函数"""
    st.markdown('<h1 class="main-header">🚀 AI音游谱面训练器</h1>', unsafe_allow_html=True)
    
    # 侧边栏
    with st.sidebar:
        st.markdown("## 🎯 训练模式")
        training_mode = st.radio(
            "选择训练模式",
            ["🆕 新训练", "🔄 增量训练", "📊 数据管理"],
            help="选择适合您需求的训练模式"
        )
        
        st.markdown("---")
        st.markdown("### 📋 快速指南")
        st.markdown("""
        **🆕 新训练**: 从头开始训练新模型
        - 适用于首次训练
        - 需要大量谱面数据
        
        **🔄 增量训练**: 在现有模型基础上继续训练
        - 适用于添加新数据
        - 保持已有知识
        
        **📊 数据管理**: 管理训练数据集
        - 查看数据统计
        - 合并数据集
        """)
    
    # 主内容区域
    if training_mode == "🆕 新训练":
        show_new_training()
    elif training_mode == "🔄 增量训练":
        show_incremental_training()
    elif training_mode == "📊 数据管理":
        show_data_management()

def show_new_training():
    """显示新训练界面"""
    st.markdown("## 🆕 从头开始训练新模型")
    
    # 数据设置
    with st.expander("📁 数据设置", expanded=True):
        col1, col2 = st.columns(2)
        
        with col1:
            chart_dir = st.text_input(
                "谱面文件目录",
                value="谱面文件",
                help="包含.mcz、.mc、.imd等格式谱面文件的目录"
            )
            
            output_dir = st.text_input(
                "处理后数据输出目录",
                value="data/processed",
                help="预处理后的训练数据保存位置"
            )
        
        with col2:
            audio_dir = st.text_input(
                "音频文件目录（可选）",
                value="",
                help="包含对应音频文件的目录，留空则自动查找"
            )
            
            skip_preprocessing = st.checkbox(
                "跳过数据预处理",
                help="如果数据已经预处理过，可以跳过此步骤"
            )
    
    # 训练参数
    with st.expander("⚙️ 训练参数", expanded=True):
        col1, col2, col3 = st.columns(3)
        
        with col1:
            epochs = st.number_input("训练轮数", min_value=1, max_value=200, value=50, step=5)
            batch_size = st.selectbox("批次大小", [2, 4, 8, 16], index=2)
        
        with col2:
            lr_g = st.selectbox("生成器学习率", [0.0001, 0.0002, 0.0005], index=1, format_func=lambda x: f"{x:.4f}")
            lr_d = st.selectbox("判别器学习率", [0.0001, 0.0002, 0.0005], index=1, format_func=lambda x: f"{x:.4f}")
        
        with col3:
            track_count = st.selectbox("轨道数量", [4, 5, 6], index=0)
            device = st.selectbox("计算设备", ["auto", "cpu", "cuda"], index=0)
    
    # 操作按钮
    col1, col2 = st.columns(2)
    
    with col1:
        if st.button("🔍 检查谱面数据", use_container_width=True):
            check_chart_data(chart_dir)
    
    with col2:
        if st.button("🚀 开始训练", type="primary", use_container_width=True):
            start_new_training(chart_dir, audio_dir, output_dir, skip_preprocessing, epochs, batch_size, lr_g, lr_d, track_count, device)

def show_incremental_training():
    """显示增量训练界面"""
    st.markdown("## 🔄 在现有模型基础上继续训练")
    
    # 模型和数据设置
    with st.expander("🤖 模型和数据设置", expanded=True):
        col1, col2 = st.columns(2)
        
        with col1:
            model_path = st.text_input("模型文件路径", value="models/best_model.pth")
            base_data_dir = st.text_input("原始训练数据目录", value="data/processed")
        
        with col2:
            new_chart_dir = st.text_input("新谱面文件目录", value="新谱面文件")
            output_dir = st.text_input("合并数据输出目录", value="data/incremental")
    
    # 训练参数
    with st.expander("⚙️ 增量训练参数", expanded=True):
        col1, col2, col3 = st.columns(3)
        
        with col1:
            epochs = st.number_input("增量训练轮数", min_value=1, max_value=100, value=20)
            batch_size = st.selectbox("批次大小", [2, 4, 8, 16], index=2)
        
        with col2:
            lr_g = st.selectbox("生成器学习率", [0.00005, 0.0001, 0.0002], index=1, format_func=lambda x: f"{x:.5f}")
            lr_d = st.selectbox("判别器学习率", [0.00005, 0.0001, 0.0002], index=1, format_func=lambda x: f"{x:.5f}")
        
        with col3:
            new_data_weight = st.slider("新数据权重", min_value=0.1, max_value=1.0, value=0.7)
            device = st.selectbox("计算设备", ["auto", "cpu", "cuda"], index=0)
    
    # 操作按钮
    col1, col2 = st.columns(2)
    
    with col1:
        if st.button("🔍 检查模型和数据", use_container_width=True):
            check_incremental_data(model_path, base_data_dir, new_chart_dir)
    
    with col2:
        if st.button("🔄 开始增量训练", type="primary", use_container_width=True):
            start_incremental_training(model_path, base_data_dir, new_chart_dir, output_dir, epochs, batch_size, lr_g, lr_d, new_data_weight, device)

def show_data_management():
    """显示数据管理界面"""
    st.markdown("## 📊 训练数据管理")
    
    # 数据统计
    with st.expander("📈 数据集统计", expanded=True):
        data_dir = st.text_input("数据集目录", value="data/processed")
        
        col1, col2 = st.columns(2)
        
        with col1:
            if st.button("📊 显示数据统计", use_container_width=True):
                show_dataset_statistics(data_dir)
        
        with col2:
            if st.button("🧹 清理重复数据", use_container_width=True):
                clean_duplicate_data(data_dir)
    
    # 数据集合并
    with st.expander("🔗 数据集合并", expanded=True):
        col1, col2, col3 = st.columns(3)
        
        with col1:
            old_data_dir = st.text_input("原数据集目录", value="data/processed")
        
        with col2:
            new_data_dir = st.text_input("新数据集目录", value="data/new_processed")
        
        with col3:
            merge_output_dir = st.text_input("合并输出目录", value="data/merged")
        
        merge_strategy = st.selectbox(
            "合并策略",
            ["merge", "replace", "append"],
            help="merge: 去重合并, replace: 新数据覆盖, append: 直接追加"
        )
        
        if st.button("🔗 合并数据集", type="primary"):
            merge_datasets_ui(old_data_dir, new_data_dir, merge_output_dir, merge_strategy)

def check_chart_data(chart_dir):
    """检查谱面数据"""
    try:
        chart_path = Path(chart_dir)
        
        if not chart_path.exists():
            st.error(f"❌ 谱面目录不存在: {chart_dir}")
            return
        
        # 查找.imd格式的谱面文件
        chart_files = []
        extensions = ['.imd']
        
        for ext in extensions:
            chart_files.extend(chart_path.glob(f"*{ext}"))
        
        if not chart_files:
            st.error(f"❌ 未找到支持的谱面文件")
            st.info(f"支持的格式: {', '.join(extensions)}")
            return
        
        st.success("✅ 谱面数据检查通过")
        
        # 显示统计信息
        col1, col2, col3 = st.columns(3)
        
        with col1:
            st.metric("总文件数", len(chart_files))
        
        with col2:
            format_stats = {}
            for file in chart_files:
                ext = file.suffix.lower()
                format_stats[ext] = format_stats.get(ext, 0) + 1
            
            st.markdown("**格式分布:**")
            for fmt, count in format_stats.items():
                st.text(f"{fmt}: {count}个")
        
        with col3:
            # 预估训练样本数
            estimated_samples = len(chart_files)
            st.metric("预估样本数", estimated_samples)
            
            if estimated_samples < 50:
                st.warning("⚠️ 样本数量较少")
            else:
                st.success("🎉 样本数量充足")
                
    except Exception as e:
        st.error(f"❌ 检查数据时出错: {e}")

def start_new_training(chart_dir, audio_dir, output_dir, skip_preprocessing, epochs, batch_size, lr_g, lr_d, track_count, device):
    """开始新训练"""
    progress_bar = st.progress(0)
    status_text = st.empty()
    
    try:
        # 构建训练命令
        cmd = [
            sys.executable, 'start_training.py',
            '--chart-dir', chart_dir,
            '--output-dir', output_dir,
            '--epochs', str(epochs),
            '--batch-size', str(batch_size),
            '--lr-g', str(lr_g),
            '--lr-d', str(lr_d),
            '--track-count', str(track_count),
            '--device', device
        ]
        
        if audio_dir:
            cmd.extend(['--audio-dir', audio_dir])
        
        if skip_preprocessing:
            cmd.append('--skip-preprocessing')
        
        status_text.text("🚀 正在启动训练...")
        progress_bar.progress(50)
        
        # 在新窗口中启动训练
        if sys.platform == "win32":
            subprocess.Popen(['start', 'cmd', '/k'] + cmd, shell=True, cwd=project_root)
        else:
            subprocess.Popen(['gnome-terminal', '--', 'bash', '-c', ' '.join(cmd) + '; read'], cwd=project_root)
        
        progress_bar.progress(100)
        status_text.text("✅ 训练已启动")
        
        st.success("🎉 训练已在新窗口中启动！")
        st.info("💡 您可以在命令行窗口中查看训练进度")
        
    except Exception as e:
        st.error(f"❌ 启动训练失败: {e}")

# 其他辅助函数的简化实现
def check_incremental_data(model_path, base_data_dir, new_chart_dir):
    """检查增量训练数据"""
    checks = [
        (Path(model_path).exists(), f"模型文件: {model_path}"),
        (Path(base_data_dir).exists(), f"基础数据: {base_data_dir}"),
        (Path(new_chart_dir).exists(), f"新谱面: {new_chart_dir}")
    ]
    
    all_passed = True
    for passed, desc in checks:
        if passed:
            st.success(f"✅ {desc}")
        else:
            st.error(f"❌ {desc}")
            all_passed = False
    
    if all_passed:
        st.success("🎉 增量训练数据检查通过！")

def start_incremental_training(model_path, base_data_dir, new_chart_dir, output_dir, epochs, batch_size, lr_g, lr_d, new_data_weight, device):
    """开始增量训练"""
    st.info("🔄 增量训练功能正在开发中...")
    st.code(f"""
# 增量训练命令示例
python manage_datasets.py prepare-incremental \\
    --base-data {base_data_dir} \\
    --new-charts {new_chart_dir} \\
    --output {output_dir}

python train_gan.py \\
    --data-dir {output_dir} \\
    --resume {model_path} \\
    --epochs {epochs} \\
    --lr-g {lr_g}
    """)

def show_dataset_statistics(data_dir):
    """显示数据集统计"""
    st.info("📊 数据统计功能正在开发中...")

def clean_duplicate_data(data_dir):
    """清理重复数据"""
    st.info("🧹 数据清理功能正在开发中...")

def merge_datasets_ui(old_data_dir, new_data_dir, merge_output_dir, merge_strategy):
    """合并数据集"""
    st.info("🔗 数据合并功能正在开发中...")

if __name__ == "__main__":
    main()
