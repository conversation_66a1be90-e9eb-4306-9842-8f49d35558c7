#!/usr/bin/env python3
"""
音频特征预提取脚本 - 大幅提升训练速度
"""

import sys
from pathlib import Path
sys.path.insert(0, str(Path(__file__).parent))

import torch
import json
import numpy as np
from tqdm import tqdm
import librosa
import pickle

def extract_audio_features(audio_path: str, target_length: int = 2000) -> torch.Tensor:
    """
    提取音频特征 - 兼容版本，处理librosa API差异

    Args:
        audio_path: 音频文件路径
        target_length: 目标长度

    Returns:
        torch.Tensor: 音频特征 [features, time]
    """
    try:
        # 加载音频
        y, sr = librosa.load(audio_path, sr=22050)

        # 如果音频太短，填充零
        if len(y) < sr:  # 少于1秒
            y = np.pad(y, (0, sr - len(y)), mode='constant')

        # 提取多种特征
        features = []
        hop_length = 512

        # 1. MFCC特征 (13维)
        mfcc = librosa.feature.mfcc(y=y, sr=sr, n_mfcc=13, hop_length=hop_length)
        features.append(mfcc)

        # 2. 色度特征 (12维) - 兼容不同librosa版本
        try:
            chroma = librosa.feature.chroma(y=y, sr=sr, hop_length=hop_length)
        except (AttributeError, TypeError):
            try:
                # 尝试chroma_stft
                chroma = librosa.feature.chroma_stft(y=y, sr=sr, hop_length=hop_length)
            except:
                # 如果都失败，创建零特征
                chroma = np.zeros((12, mfcc.shape[1]))
        features.append(chroma)

        # 3. 光谱质心 (1维)
        try:
            spectral_centroids = librosa.feature.spectral_centroid(y=y, sr=sr, hop_length=hop_length)
        except:
            spectral_centroids = np.zeros((1, mfcc.shape[1]))
        features.append(spectral_centroids)

        # 4. 光谱带宽 (1维)
        try:
            spectral_bandwidth = librosa.feature.spectral_bandwidth(y=y, sr=sr, hop_length=hop_length)
        except:
            spectral_bandwidth = np.zeros((1, mfcc.shape[1]))
        features.append(spectral_bandwidth)

        # 5. 光谱滚降 (1维)
        try:
            spectral_rolloff = librosa.feature.spectral_rolloff(y=y, sr=sr, hop_length=hop_length)
        except:
            spectral_rolloff = np.zeros((1, mfcc.shape[1]))
        features.append(spectral_rolloff)

        # 6. 零交叉率 (1维)
        try:
            zcr = librosa.feature.zero_crossing_rate(y, hop_length=hop_length)
        except:
            zcr = np.zeros((1, mfcc.shape[1]))
        features.append(zcr)

        # 7. RMS能量 (1维)
        try:
            rms = librosa.feature.rms(y=y, hop_length=hop_length)
        except:
            rms = np.zeros((1, mfcc.shape[1]))
        features.append(rms)

        # 8. 梅尔频谱 (64维)
        try:
            mel_spec = librosa.feature.melspectrogram(y=y, sr=sr, n_mels=64, hop_length=hop_length)
            mel_spec_db = librosa.power_to_db(mel_spec, ref=np.max)
        except:
            mel_spec_db = np.zeros((64, mfcc.shape[1]))
        features.append(mel_spec_db)
        
        # 合并所有特征
        audio_features = np.concatenate(features, axis=0)  # [94, time]
        
        # 调整时间维度到目标长度
        current_length = audio_features.shape[1]
        if current_length > target_length:
            # 截断
            audio_features = audio_features[:, :target_length]
        elif current_length < target_length:
            # 填充
            pad_length = target_length - current_length
            padding = np.zeros((audio_features.shape[0], pad_length))
            audio_features = np.concatenate([audio_features, padding], axis=1)
        
        return torch.tensor(audio_features, dtype=torch.float32)
        
    except Exception as e:
        print(f"提取音频特征失败 {audio_path}: {e}")
        # 返回零特征
        return torch.zeros(94, target_length, dtype=torch.float32)

def preprocess_all_audio(data_dir: str):
    """预处理所有音频文件"""
    data_dir = Path(data_dir)
    
    print("🎵 开始预提取音频特征...")
    print(f"📂 数据目录: {data_dir}")
    
    # 扫描所有音频文件
    audio_files = list(data_dir.rglob("*.mp3"))
    print(f"🔍 找到 {len(audio_files)} 个音频文件")
    
    features_cache = {}
    
    for audio_file in tqdm(audio_files, desc="提取音频特征"):
        try:
            # 提取特征
            features = extract_audio_features(str(audio_file))
            
            # 保存到缓存 - 使用相对路径作为key
            relative_path = str(audio_file.relative_to(data_dir))
            features_cache[relative_path] = features
            
        except Exception as e:
            print(f"❌ 提取失败: {audio_file.name} - {e}")
            continue
    
    # 保存特征缓存 - 使用pickle格式更高效
    cache_file = data_dir / "audio_features_cache.pkl"
    with open(cache_file, 'wb') as f:
        pickle.dump(features_cache, f)
    
    print(f"✅ 特征提取完成！")
    print(f"💾 缓存文件: {cache_file}")
    print(f"📊 成功提取: {len(features_cache)} / {len(audio_files)} 个文件")
    print(f"💽 缓存大小: {cache_file.stat().st_size / 1024 / 1024:.1f} MB")

if __name__ == "__main__":
    import argparse
    
    parser = argparse.ArgumentParser(description="预提取音频特征")
    parser.add_argument("--data-dir", required=True, help="数据目录")
    
    args = parser.parse_args()
    preprocess_all_audio(args.data_dir)
