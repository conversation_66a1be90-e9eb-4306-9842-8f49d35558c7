#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import json
from collections import defaultdict

def check_note_overlaps(json_file):
    """检测谱面中的音符重叠问题"""
    
    with open(json_file, 'r', encoding='utf-8') as f:
        data = json.load(f)
    
    notes = data['notes']
    overlaps = []
    
    # 按轨道分组
    tracks = defaultdict(list)
    for note in notes:
        tracks[note['track']].append(note)
    
    # 检查每个轨道的重叠
    for track_num, track_notes in tracks.items():
        # 按时间排序
        track_notes.sort(key=lambda x: x['time'])
        
        print(f"\n=== 轨道 {track_num} ===")
        print(f"总音符数: {len(track_notes)}")
        
        # 检测重叠
        active_holds = []  # 当前活跃的长音符
        track_overlaps = []
        
        for note in track_notes:
            current_time = note['time']
            
            # 移除已结束的长音符
            active_holds = [hold for hold in active_holds 
                          if hold['time'] + hold['duration'] > current_time]
            
            # 检查是否与活跃的长音符重叠
            for active_hold in active_holds:
                overlap_info = {
                    'track': track_num,
                    'time': current_time,
                    'new_note': note,
                    'conflicting_hold': active_hold,
                    'hold_end_time': active_hold['time'] + active_hold['duration']
                }
                track_overlaps.append(overlap_info)
                overlaps.append(overlap_info)
                
                print(f"⚠️  重叠发现:")
                print(f"   时间: {current_time}ms")
                print(f"   新音符: {note['type']}")
                print(f"   冲突长音符: {active_hold['type']} (开始:{active_hold['time']}ms, 结束:{active_hold['time'] + active_hold['duration']}ms)")
            
            # 如果是长音符，加入活跃列表
            if note['type'] == 'hold' and note['duration'] > 0:
                active_holds.append(note)
        
        print(f"轨道 {track_num} 重叠数量: {len(track_overlaps)}")
    
    print(f"\n=== 总结 ===")
    print(f"总重叠数量: {len(overlaps)}")
    print(f"总音符数量: {len(notes)}")
    print(f"重叠比例: {len(overlaps)/len(notes)*100:.2f}%")
    
    # 显示前10个重叠的详细信息
    if overlaps:
        print(f"\n=== 前10个重叠详情 ===")
        for i, overlap in enumerate(overlaps[:10]):
            print(f"{i+1}. 轨道{overlap['track']} 时间{overlap['time']}ms:")
            print(f"   新音符: {overlap['new_note']['type']}")
            print(f"   冲突: {overlap['conflicting_hold']['type']} ({overlap['conflicting_hold']['time']}-{overlap['hold_end_time']}ms)")
    
    return overlaps

if __name__ == "__main__":
    overlaps = check_note_overlaps("output.json")
