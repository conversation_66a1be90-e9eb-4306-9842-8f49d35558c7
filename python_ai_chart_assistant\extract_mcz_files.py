#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
批量解压mcz文件的脚本
"""

import os
import zipfile
import shutil
from pathlib import Path

def extract_mcz_files():
    """批量解压mcz文件到解压文件夹"""

    # 设置路径
    script_dir = Path(__file__).parent
    source_dir = script_dir / "谱面文件"
    extract_dir = script_dir / "谱面文件" / "解压"
    
    # 确保解压目录存在
    extract_dir.mkdir(exist_ok=True)
    
    # 获取所有mcz文件
    mcz_files = list(source_dir.glob("*.mcz"))
    
    print(f"找到 {len(mcz_files)} 个mcz文件")
    
    success_count = 0
    error_count = 0
    
    for mcz_file in mcz_files:
        try:
            # 跳过正在下载的文件
            if mcz_file.name.endswith('.downloading'):
                print(f"跳过正在下载的文件: {mcz_file.name}")
                continue
                
            print(f"正在解压: {mcz_file.name}")
            
            # 创建以文件名命名的子文件夹
            file_name = mcz_file.stem  # 去掉.mcz扩展名
            target_folder = extract_dir / file_name
            target_folder.mkdir(exist_ok=True)
            
            # 解压文件
            with zipfile.ZipFile(mcz_file, 'r') as zip_ref:
                zip_ref.extractall(target_folder)
            
            print(f"成功解压: {mcz_file.name} -> {target_folder}")
            success_count += 1
            
        except zipfile.BadZipFile:
            print(f"错误: {mcz_file.name} 不是有效的zip文件")
            error_count += 1
        except Exception as e:
            print(f"解压 {mcz_file.name} 时出错: {str(e)}")
            error_count += 1
    
    print(f"\n解压完成!")
    print(f"成功: {success_count} 个文件")
    print(f"失败: {error_count} 个文件")

if __name__ == "__main__":
    extract_mcz_files()
