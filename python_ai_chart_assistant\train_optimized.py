#!/usr/bin/env python3
"""
优化版GAN训练脚本 - 修复损失函数和超参数问题
基于前一次训练的经验进行针对性优化
"""

import argparse
import sys
import logging
from pathlib import Path
import torch
import torch.nn as nn
import torch.optim as optim
from torch.utils.data import DataLoader, random_split
from tqdm import tqdm
import gc
import time

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from src.training.audio_chart_dataset import AudioChartDataset
from src.models.audio_chart_gan import AudioChartGAN

# 设置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def optimize_pytorch():
    """PyTorch性能优化设置"""
    torch.backends.cudnn.benchmark = True
    torch.set_num_threads(4)
    torch.autograd.set_detect_anomaly(False)
    print("✅ PyTorch性能优化已启用")

def improved_music_matching_loss(fake_charts, audio_features):
    """改进的音乐匹配损失 - 解决数值过大问题"""
    batch_size = fake_charts.size(0)

    # 检查张量维度
    # fake_charts: [batch, time, tracks]
    # audio_features: [batch, features, time]

    # 1. 节拍对齐损失 - 简化版本
    # 计算谱面的节拍密度 - 对轨道维度求平均
    chart_density = torch.mean(fake_charts, dim=2)  # [batch, time]

    # 计算音频的能量特征 - 使用RMS相关的特征
    # 假设audio_features的最后几个维度是能量相关的
    audio_energy = torch.mean(audio_features[:, -5:, :], dim=1)  # [batch, time] 使用最后5个特征维度

    # 确保时间维度匹配
    chart_time = chart_density.size(1)
    audio_time = audio_energy.size(1)

    if audio_time != chart_time:
        # 调整音频特征的时间维度
        if audio_time > chart_time:
            audio_energy = audio_energy[:, :chart_time]
        else:
            # 填充
            padding = chart_time - audio_time
            pad_tensor = torch.zeros(batch_size, padding, device=audio_energy.device)
            audio_energy = torch.cat([audio_energy, pad_tensor], dim=1)

    # 计算相关性损失
    rhythm_loss = nn.MSELoss()(chart_density, audio_energy)

    # 2. 强度匹配损失
    # 计算谱面的总强度
    chart_intensity = torch.sum(fake_charts, dim=2)  # [batch, time] 所有轨道的总和

    # 使用MFCC特征作为音频强度指标
    audio_intensity = torch.mean(audio_features[:, :13, :], dim=1)  # [batch, time] 使用前13维MFCC

    # 确保时间维度匹配
    if audio_intensity.size(1) != chart_intensity.size(1):
        if audio_intensity.size(1) > chart_intensity.size(1):
            audio_intensity = audio_intensity[:, :chart_intensity.size(1)]
        else:
            padding = chart_intensity.size(1) - audio_intensity.size(1)
            pad_tensor = torch.zeros(batch_size, padding, device=audio_intensity.device)
            audio_intensity = torch.cat([audio_intensity, pad_tensor], dim=1)

    intensity_loss = nn.MSELoss()(chart_intensity, audio_intensity)

    # 归一化到合理范围 - 进一步缩小
    total_loss = (rhythm_loss + intensity_loss) * 1.0  # 更小的缩放因子

    return total_loss

def optimized_train_step(model, real_charts, audio_features, target_quality, target_difficulty, 
                        optimizer_d, optimizer_g, device, loss_weights=None):
    """优化的训练步骤 - 修复损失函数和学习策略"""
    batch_size = real_charts.size(0)
    
    # 优化的损失权重
    if loss_weights is None:
        loss_weights = {
            'adversarial': 1.0,
            'quality': 0.5,
            'difficulty': 0.3,
            'music_matching': 0.1,  # 大幅降低音乐匹配损失权重
        }
    
    # ===== 训练判别器 =====
    optimizer_d.zero_grad()
    
    # 真实数据
    real_output = model.discriminator(real_charts)
    
    # 质量损失
    quality_target = target_quality.float().unsqueeze(1) if target_quality.dim() == 1 else target_quality.float()
    quality_loss = nn.SmoothL1Loss()(real_output['quality'], quality_target)
    
    # 难度损失
    difficulty_target = target_difficulty.long()
    difficulty_loss = nn.CrossEntropyLoss()(real_output['difficulty'], difficulty_target)
    
    # 假数据
    with torch.no_grad():
        fake_charts_probs = model.generator(audio_features)
        fake_charts = model._constrained_sampling(fake_charts_probs)
    
    fake_output = model.discriminator(fake_charts)
    
    # 标准GAN损失 - 添加标签平滑
    real_labels = torch.ones(batch_size, 1, device=device) * 0.9  # 标签平滑
    fake_labels = torch.zeros(batch_size, 1, device=device) + 0.1  # 标签平滑
    d_real_loss = nn.BCELoss()(real_output['real_fake'], real_labels)
    d_fake_loss = nn.BCELoss()(fake_output['real_fake'], fake_labels)
    d_adversarial_loss = (d_real_loss + d_fake_loss) / 2
    
    # 总判别器损失
    d_loss = (loss_weights['adversarial'] * d_adversarial_loss + 
              loss_weights['quality'] * quality_loss + 
              loss_weights['difficulty'] * difficulty_loss)
    
    d_loss.backward()
    torch.nn.utils.clip_grad_norm_(model.discriminator.parameters(), max_norm=0.5)  # 更严格的梯度裁剪
    optimizer_d.step()
    
    # ===== 训练生成器 (每2步训练1次) =====
    train_generator = True  # 可以添加条件控制
    
    if train_generator:
        optimizer_g.zero_grad()
        
        # 重新生成假数据
        fake_charts_probs = model.generator(audio_features)
        fake_charts = model._constrained_sampling(fake_charts_probs)
        fake_output = model.discriminator(fake_charts)
        
        # 生成器对抗损失
        g_adversarial_loss = nn.BCELoss()(fake_output['real_fake'], torch.ones(batch_size, 1, device=device))
        
        # 改进的音乐匹配损失
        music_loss = improved_music_matching_loss(fake_charts, audio_features)
        
        # 特征匹配损失 - 新增
        with torch.no_grad():
            real_features = model.discriminator.get_features(real_charts)
        fake_features = model.discriminator.get_features(fake_charts)
        feature_matching_loss = nn.MSELoss()(fake_features, real_features)
        
        # 总生成器损失
        total_g_loss = (loss_weights['adversarial'] * g_adversarial_loss + 
                        loss_weights['music_matching'] * music_loss +
                        0.1 * feature_matching_loss)  # 特征匹配权重
        
        total_g_loss.backward()
        torch.nn.utils.clip_grad_norm_(model.generator.parameters(), max_norm=0.5)
        optimizer_g.step()
    else:
        total_g_loss = torch.tensor(0.0)
        music_loss = torch.tensor(0.0)
        feature_matching_loss = torch.tensor(0.0)
    
    return {
        'd_loss': d_loss.item(),
        'g_loss': total_g_loss.item(),
        'music_loss': music_loss.item(),
        'quality_loss': quality_loss.item(),
        'difficulty_loss': difficulty_loss.item(),
        'feature_matching_loss': feature_matching_loss.item() if isinstance(feature_matching_loss, torch.Tensor) else 0.0,
    }

def train_optimized_gan(data_dir, save_dir, epochs=10, batch_size=16, lr_d=0.0001, lr_g=0.0002):
    """优化版GAN训练 - 修复超参数和训练策略"""
    
    # 性能优化设置
    optimize_pytorch()
    
    device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
    print(f"🚀 使用设备: {device}")
    
    # 检查预提取特征
    cache_file = Path(data_dir) / "audio_features_cache.pkl"
    if not cache_file.exists():
        print("⚠️  未找到音频特征缓存，建议先运行:")
        print(f"   python preprocess_audio_simple.py --data-dir {data_dir}")
        print("🔄 继续使用实时特征提取...")
    else:
        print(f"✅ 使用音频特征缓存: {cache_file}")
    
    # 创建数据集
    print("📊 创建数据集...")
    dataset = AudioChartDataset(data_dir, augment=False)
    print(f"✅ 数据集: {len(dataset)} 个样本")
    
    # 分割数据集
    total_size = len(dataset)
    train_size = int(total_size * 0.8)
    val_size = total_size - train_size
    train_dataset, val_dataset = random_split(dataset, [train_size, val_size])
    
    # 优化的DataLoader
    num_workers = min(4, batch_size)
    train_loader = DataLoader(
        train_dataset, 
        batch_size=batch_size, 
        shuffle=True, 
        num_workers=num_workers,
        pin_memory=True if device.type == 'cuda' else False,
        persistent_workers=True if num_workers > 0 else False,
        drop_last=True
    )
    
    print(f"🎯 优化训练配置:")
    print(f"   - 训练集: {len(train_dataset)}")
    print(f"   - 验证集: {len(val_dataset)}")
    print(f"   - 批次大小: {batch_size}")
    print(f"   - 判别器学习率: {lr_d}")
    print(f"   - 生成器学习率: {lr_g}")
    
    # 创建模型
    print("🧠 创建模型...")
    model = AudioChartGAN(track_count=4, max_length=2000, use_wgan=False).to(device)
    
    # 优化的优化器 - 不同学习率
    optimizer_d = optim.Adam(model.discriminator.parameters(), lr=lr_d, betas=(0.5, 0.999))
    optimizer_g = optim.Adam(model.generator.parameters(), lr=lr_g, betas=(0.5, 0.999))
    
    # 学习率调度器
    scheduler_d = optim.lr_scheduler.ReduceLROnPlateau(optimizer_d, mode='min', patience=3, factor=0.5)
    scheduler_g = optim.lr_scheduler.ReduceLROnPlateau(optimizer_g, mode='min', patience=3, factor=0.5)
    
    print(f"🎯 开始优化训练 {epochs} 轮...")
    
    # 训练循环
    start_time = time.time()
    best_g_loss = float('inf')
    
    for epoch in range(epochs):
        model.train()
        epoch_d_loss = 0
        epoch_g_loss = 0
        epoch_music_loss = 0
        
        progress_bar = tqdm(train_loader, desc=f"Epoch {epoch+1}/{epochs}")
        
        for batch_idx, batch in enumerate(progress_bar):
            real_charts = batch['chart'].to(device, non_blocking=True)
            audio_features = batch['audio_features'].to(device, non_blocking=True)
            target_quality = batch['quality'].to(device, non_blocking=True)
            target_difficulty = batch['difficulty'].to(device, non_blocking=True)
            
            # 优化的训练步骤
            losses = optimized_train_step(
                model, real_charts, audio_features, target_quality, target_difficulty,
                optimizer_d, optimizer_g, device
            )
            
            epoch_d_loss += losses['d_loss']
            epoch_g_loss += losses['g_loss']
            epoch_music_loss += losses['music_loss']
            
            # 更新进度条
            progress_bar.set_postfix({
                'D': f"{losses['d_loss']:.3f}",
                'G': f"{losses['g_loss']:.3f}",
                'M': f"{losses['music_loss']:.3f}",
                'FM': f"{losses['feature_matching_loss']:.3f}"
            })
            
            # 定期清理显存
            if batch_idx % 10 == 0:
                torch.cuda.empty_cache()
        
        # Epoch统计
        avg_d_loss = epoch_d_loss / len(train_loader)
        avg_g_loss = epoch_g_loss / len(train_loader)
        avg_music_loss = epoch_music_loss / len(train_loader)
        
        # 学习率调度
        scheduler_d.step(avg_d_loss)
        scheduler_g.step(avg_g_loss)
        
        elapsed_time = time.time() - start_time
        epoch_time = elapsed_time / (epoch + 1)
        remaining_time = epoch_time * (epochs - epoch - 1)
        
        print(f"Epoch {epoch+1}/{epochs} 完成:")
        print(f"  判别器损失: {avg_d_loss:.4f}")
        print(f"  生成器损失: {avg_g_loss:.4f}")
        print(f"  音乐匹配损失: {avg_music_loss:.4f}")
        print(f"  判别器学习率: {optimizer_d.param_groups[0]['lr']:.6f}")
        print(f"  生成器学习率: {optimizer_g.param_groups[0]['lr']:.6f}")
        print(f"  用时: {epoch_time:.1f}s, 剩余: {remaining_time/60:.1f}min")
        
        # 保存最佳模型
        if avg_g_loss < best_g_loss:
            best_g_loss = avg_g_loss
            save_path = Path(save_dir) / f"optimized_gan_best.pth"
            save_path.parent.mkdir(parents=True, exist_ok=True)
            torch.save({
                'epoch': epoch + 1,
                'model_state_dict': model.state_dict(),
                'optimizer_d_state_dict': optimizer_d.state_dict(),
                'optimizer_g_state_dict': optimizer_g.state_dict(),
                'best_g_loss': best_g_loss,
            }, save_path)
            print(f"💾 最佳模型已保存: {save_path}")
        
        # 定期保存
        if (epoch + 1) % 5 == 0:
            save_path = Path(save_dir) / f"optimized_gan_epoch_{epoch+1}.pth"
            save_path.parent.mkdir(parents=True, exist_ok=True)
            torch.save({
                'epoch': epoch + 1,
                'model_state_dict': model.state_dict(),
                'optimizer_d_state_dict': optimizer_d.state_dict(),
                'optimizer_g_state_dict': optimizer_g.state_dict(),
            }, save_path)
            print(f"💾 检查点已保存: {save_path}")
        
        # 清理内存
        gc.collect()
        torch.cuda.empty_cache()
    
    total_time = time.time() - start_time
    print(f"🎉 优化训练完成！总用时: {total_time/60:.1f}分钟")
    print(f"⚡ 平均每轮: {total_time/epochs:.1f}秒")
    print(f"🏆 最佳生成器损失: {best_g_loss:.4f}")

if __name__ == "__main__":
    parser = argparse.ArgumentParser(description="优化版GAN训练")
    parser.add_argument("--data-dir", required=True, help="数据目录")
    parser.add_argument("--save-dir", required=True, help="保存目录")
    parser.add_argument("--epochs", type=int, default=10, help="训练轮数")
    parser.add_argument("--batch-size", type=int, default=16, help="批次大小")
    parser.add_argument("--lr-d", type=float, default=0.0001, help="判别器学习率")
    parser.add_argument("--lr-g", type=float, default=0.0002, help="生成器学习率")
    
    args = parser.parse_args()
    
    try:
        train_optimized_gan(
            data_dir=args.data_dir,
            save_dir=args.save_dir,
            epochs=args.epochs,
            batch_size=args.batch_size,
            lr_d=args.lr_d,
            lr_g=args.lr_g
        )
    except Exception as e:
        logger.error(f"训练失败: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)
